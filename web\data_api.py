import datetime
import json
from math import floor
import os

from datetime import datetime

from fastapi import APIRouter, Request, Depends, HTTPException, status
from fastapi.responses import J<PERSON>NResponse

from app.helper.meta_helper import MetaH<PERSON>per
import log

from app.brushtask import BrushTask
from app.conf.moduleconf import ModuleConf
from app.conf.systemconfig import SystemConfig
from app.downloader.downloader import Downloader
from app.filter import Filter
from app.indexer.indexer import Indexer
from app.message import Message
from app.rsschecker import RssChecker
from app.sites.site_userinfo import SiteUserInfo
from app.sites.sites import Sites
from app.sync import Sync
from app.torrentremover import TorrentRemover
from app.utils.system_utils import SystemUtils
from app.utils.types import *

from app.mediaserver.media_server import MediaServer
from config import PT_TRANSFER_INTERVAL, TMDB_API_DOMAINS, Config
from web.action import WebAction
from web.backend.security import decode_access_token
from web.backend.user import User, User<PERSON>anager
from web.backend.wallpaper import get_login_wallpaper
from web.backend.web_utils import WebUtils


# API路由
data_router = APIRouter(prefix="/data")

async def get_current_user(request: Request):
    token = request.cookies.get("access_token")
    if not token:
        raise HTTPException(status_code=401, detail="缺少 access_token")
    username = decode_access_token(token)
    if username is None:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="无效 token",
            headers={"WWW-Authenticate": "Bearer"},
        )
    user = UserManager().get_user_by_name(username)
    if user is None:
        raise HTTPException(status_code=404, detail="用户不存在")
    return user

# 封装一个统一的返回函数
def response(code: int = 0, msg: str = "success", data=None, status_code: int = 200):
    return JSONResponse(
        content={
            "code": code,
            "msg": msg,
            "data": data
        },
        status_code=status_code
    )

# sysinfo
@data_router.post("/sysinfo")
async def sysinfo(request: Request, current_user: User = Depends(get_current_user)):

    # 判断当前的运营环境
    system_flag = SystemUtils.get_system()
    tmdb_flag = 1 if Config().get_config('app').get('rmt_tmdbkey') else 0
    default_path = Config().get_config('media').get('media_default_path')
    sync_mod = Config().get_config('media').get('default_rmt_mode')
    if not sync_mod:
        sync_mod = "link"

    commands = WebAction().get_commands()
    image_code, img_title, img_link = get_login_wallpaper()
    username = current_user.username

    return response(data=
        {
            "username" : username,
            "admin" : 1 if username == 'admin' else 0,
            "search" : current_user.search,
            "imgLink" : img_link,
            "SystemFlag": system_flag.value,
            "TMDBFlag": tmdb_flag,
            "AppVersion": WebUtils.get_current_version(),
            "SyncMod": sync_mod,
            "DefaultPath": default_path,
            "Commands": commands
        }
    )


# rss_sysdict
@data_router.post("/base_sys_dict")
async def base_sys_dict(request: Request, current_user: User = Depends(get_current_user)):

    restype_dict = ModuleConf.TORRENT_SEARCH_PARAMS.get("restype")
    pix_dict = ModuleConf.TORRENT_SEARCH_PARAMS.get("pix")
    indexers = Indexer().get_indexers()

    return response(data=
        {
            "RestypeDict": restype_dict,
            "PixDict": pix_dict,
            "Indexers": indexers,
        }
    )


# 全量转移模式
@data_router.post("/rmt_modes")
async def rmt_modes(request: Request, current_user: User = Depends(get_current_user)):
    rmt_mode_dict = WebAction().get_rmt_modes()
    return response(data=
        {
            "RmtModeDict": rmt_mode_dict,
        }
    )

# 基础设置页面
@data_router.post("/basic")
async def basic(request: Request, current_user: User = Depends(get_current_user)):
    proxy = Config().get_config('app').get("proxies", {}).get("http")
    if proxy:
        proxy = proxy.replace("http://", "")
        
    RmtModeDict = WebAction().get_rmt_modes()
    CustomScriptCfg = SystemConfig().get(SystemConfigKey.CustomScript)
    ScraperConf = SystemConfig().get(SystemConfigKey.UserScraperConf) or {}
    return response(data=
        {
            "Config": Config().get_config(),
            "Proxy": proxy,
            "RmtModeDict": RmtModeDict,
            "CustomScriptCfg": CustomScriptCfg,
            "CurrentUser": current_user,
            "ScraperNfo": ScraperConf.get("scraper_nfo") or {},
            "ScraperPic": ScraperConf.get("scraper_pic") or {},
            "MediaServerConf": ModuleConf.MEDIASERVER_CONF,
            "TmdbDomains": TMDB_API_DOMAINS,
        }
    )


# 开始页面
@data_router.post("/index")
async def index(request: Request, current_user: User = Depends(get_current_user)):

    # 媒体服务器类型
    MSType = Config().get_config('media').get('media_server')
    # 获取媒体数量
    MediaCounts = WebAction().get_library_mediacount()
    if MediaCounts.get("code") == 0:
        ServerSucess = True
    else:
        ServerSucess = False

    # 获得活动日志
    Activity = WebAction().get_library_playhistory().get("result")

    # 磁盘空间
    LibrarySpaces = WebAction().get_library_spacesize()

    # 媒体库
    Librarys = MediaServer().get_libraries()
    LibrarySyncConf = SystemConfig().get(SystemConfigKey.SyncLibrary) or []

    # 继续观看
    Resumes = MediaServer().get_resume()

    # 最近添加
    Latests = MediaServer().get_latest()

    return response(data=
        {
             "ServerSucess": ServerSucess,
             "MediaCount": {'MovieCount': MediaCounts.get("Movie"),
                     'SeriesCount': MediaCounts.get("Series"),
                     'SongCount': MediaCounts.get("Music"),
                     "EpisodeCount": MediaCounts.get("Episodes")
                     },
             "Activitys": Activity,
             "UserCount": MediaCounts.get("User"),
             "FreeSpace": LibrarySpaces.get("FreeSpace"),
             "TotalSpace": LibrarySpaces.get("TotalSpace"),
             "UsedSapce": LibrarySpaces.get("UsedSapce"),
             "UsedPercent": LibrarySpaces.get("UsedPercent"),
             "MediaServerType": MSType,
             "Librarys": Librarys,
             "LibrarySyncConf": LibrarySyncConf,
             "Resumes": Resumes,
             "Latests": Latests,
        }
    )

# 资源搜索页面
@data_router.post("/search")
async def search(request: Request, current_user: User = Depends(get_current_user)):

    # 权限
    username = current_user.username
    pris = UserManager().get_user_by_name(username).pris

    # 结果
    res = WebAction().get_search_result()
    SearchResults = res.get("result")
    Count = res.get("total")

    return response(data=
        {
            "UserPris": str(pris).split(","),
            "Count": Count,
            "Results": SearchResults,
            "SiteDict": Indexer().get_indexer_hash_dict(),
            "UPCHAR": chr(8593),
        })

# 电影订阅页面
@data_router.post("/movie_rss")
async def movie_rss(request: Request, current_user: User = Depends(get_current_user)):

    RssItems = WebAction().get_movie_rss_list().get("result")
    RuleGroups = {str(group["id"]): group["name"] for group in Filter().get_rule_groups()}
    DownloadSettings = Downloader().get_download_setting()

    return response(data=
        {
            "CurrentUser": current_user,
            "Count": len(RssItems),
            "RuleGroups": RuleGroups,
            "DownloadSettings": DownloadSettings,
            "Items": RssItems,
            "Type": 'MOV',
            "TypeName": '电影',
        })

# 电视剧订阅页面
@data_router.post("/tv_rss")
async def tv_rss(request: Request, current_user: User = Depends(get_current_user)):

    RssItems = WebAction().get_tv_rss_list().get("result")
    RuleGroups = {str(group["id"]): group["name"] for group in Filter().get_rule_groups()}
    DownloadSettings = Downloader().get_download_setting()

    return response(data=
        {
            "CurrentUser": current_user,
            "Count": len(RssItems),
            "RuleGroups": RuleGroups,
            "DownloadSettings": DownloadSettings,
            "Items": RssItems,
            "Type": 'TV',
            "TypeName": '电视剧',
        })


# 订阅历史页面
@data_router.post("/rss_history")
async def rss_history(request: Request, current_user: User = Depends(get_current_user), t: str = ""):

    RssHistory = WebAction().get_rss_history({"type": t}).get("result")

    return response(data=
        {
            "CurrentUser": current_user,
            "Count": len(RssHistory),
            "Items": RssHistory,
            "Type": t,
        })


# 订阅日历页面
@data_router.post("/rss_calendar")
async def rss_calendar(request: Request, current_user: User = Depends(get_current_user)):

    Today = datetime.strftime(datetime.now(), '%Y-%m-%d')
    # 电影订阅
    RssMovieItems = WebAction().get_movie_rss_items().get("result")
    # 电视剧订阅
    RssTvItems = WebAction().get_tv_rss_items().get("result")

    return response(data=
        {
            "CurrentUser": current_user,
            "Today": Today,
            "RssMovieItems": RssMovieItems,
            "RssTvItems": RssTvItems,
        })


# 索引站点页面
@data_router.post("/indexer")
async def indexer(request: Request, current_user: User = Depends(get_current_user), public: int = 1):

    indexers = Indexer().get_indexers(check=False)
    indexer_sites = SystemConfig().get(SystemConfigKey.UserIndexerSites)
    if not indexer_sites:
        indexer_sites = []

    DownloadSettings = {did: attr["name"] for did, attr in Downloader().get_download_setting().items()}
    SourceTypes = { "MOVIE":'电影', "TV":'剧集', "ANIME":'动漫' }
    SearchTypes = { "title":'关键字', "en_name":'英文名', "douban_id":'豆瓣id', "imdb":'imdb id' }

    front_indexers = []
    check_sites = []

    is_public = public == 1

    for idx_site in indexers:
        checked = idx_site.id in indexer_sites
        if checked:
            check_sites.append(idx_site.id)

        if idx_site.public != is_public:
            continue
        site_info = {
            "id": idx_site.id,
            "name": idx_site.name,
            "domain": idx_site.domain,
            "render": idx_site.render,
            "source_type": idx_site.source_type,
            "search_type": SearchTypes.get(idx_site.search_type, '关键字'),
            "downloader": idx_site.downloader,
            "public": idx_site.public,
            "proxy": idx_site.proxy,
            "en_expand": idx_site.en_expand,
            "checked": checked
        }
        front_indexers.append(site_info)

    return response(data=
        {
            "CurrentUser": current_user,
            "Config": Config().get_config(),
            "IsPublic": public,
            "Indexers": front_indexers,
            "DownloadSettings": DownloadSettings,
            "CheckSites": check_sites,
            "SourceTypes": SourceTypes,
        })


# PT索引站点页面
@data_router.post("/ptindexer")
async def ptindexer(request: Request, current_user: User = Depends(get_current_user)):

    indexers = Indexer().get_indexers(check=False)
    indexer_sites = SystemConfig().get(SystemConfigKey.UserIndexerSites)
    if not indexer_sites:
        indexer_sites = []

    SearchTypes = { "title":'关键字', "en_name":'英文名', "douban_id":'豆瓣id', "imdb":'imdb id' }

    private_indexers = []
    check_sites = []
    for site in indexers:
        checked = site.id in indexer_sites
        if checked:
            check_sites.append(site.id)
        if site.public:
            continue
        site_info = {
            "id": site.id,
            "name": site.name,
            "domain": site.domain,
            "render": site.render,
            "source_type": site.source_type,
            "search_type": SearchTypes.get(site.search_type, '关键字'),
            "downloader": site.downloader,
            "public": site.public,
            "proxy": site.proxy,
            "en_expand": site.en_expand,
            "checked": checked
        }
        private_indexers.append(site_info)

    DownloadSettings = {did: attr["name"] for did, attr in Downloader().get_download_setting().items()}
    SourceTypes = { "MOVIE":'电影', "TV":'剧集', "ANIME":'动漫' }

    return response(data=
        {
            "CurrentUser": current_user,
            "Config": Config().get_config(),
            "IsPublic": 0,
            "Indexers": private_indexers,
            "DownloadSettings": DownloadSettings,
            "CheckSites": check_sites,
            "SourceTypes": SourceTypes,
            "SearchTypes": SearchTypes,
        })

# 站点维护页面
@data_router.post("/site")
async def sites_page(request: Request, current_user = Depends(get_current_user)):
    cfg_sites = Sites().get_sites()
    rule_groups = {str(group["id"]): group["name"] for group in Filter().get_rule_groups()}
    download_settings = {did: attr["name"] for did, attr in Downloader().get_download_setting().items()}
    cookie_cloud_cfg = SystemConfig().get(SystemConfigKey.CookieCloud)
    cookie_user_info_cfg = SystemConfig().get(SystemConfigKey.CookieUserInfo)

    return response(data=
        {
            "Sites": cfg_sites,
            "RuleGroups": rule_groups,
            "DownloadSettings": download_settings,
            "ChromeOk": True,
            "CookieCloudCfg": cookie_cloud_cfg,
            "CookieUserInfoCfg": cookie_user_info_cfg,
            "CurrentUser": current_user,
        }
    )


# 站点资源页面
@data_router.post("/sitelist")
async def sitelist_page(request: Request, current_user = Depends(get_current_user)):
    indexer_sites = Indexer().get_indexers(check=False)
    return response(data=
        {
            "Sites": indexer_sites,
            "Count": len(indexer_sites),
            "CurrentUser": current_user,
        }
    )


# 站点资源页面
@data_router.post("/resources")
async def resources(request: Request, current_user = Depends(get_current_user), site: str = "", title: str = "", page: int = 0, keyword: str = ""):

    Results = WebAction().list_site_resources({
        "id": site,
        "page": page,
        "keyword": keyword
    }).get("data") or []

    return response(data=
        {
            "SiteId": site,
            "Title": title,
            "Results": Results,
            "Count": len(Results),
            "KeyWord": keyword,
            "CurrentPage": int(page),
            "TotalPage": int(page) + 1,
            "CurrentUser": current_user,
        })


# 媒体库页面
@data_router.post("/library")
async def library(request: Request, current_user = Depends(get_current_user)):
    return response(data=
        {
            "Config": Config().get_config(),
            "MediaServerConf": ModuleConf.MEDIASERVER_CONF,
        })


# 通知消息页面
@data_router.post("/notification")
async def notification(request: Request, current_user = Depends(get_current_user)):
    MessageClients = Message().get_message_client_info()
    Switchs = ModuleConf.MESSAGE_CONF.get("switch")

    Channels = ModuleConf.MESSAGE_CONF.get("client")
    ChannelsTpyes = []
    # 遍历修改
    for key, conf in Channels.items():
        if "search_type" in conf:
            conf["search_type"] = str(conf["search_type"])
        ChannelsTpyes.append(key)

    return response(data=
        {
            "Channels": Channels,
            "Switchs": Switchs,
            "ChannelsTpyes": ChannelsTpyes,
            "ClientCount": len(MessageClients),
            "MessageClients": MessageClients,
        })


# 用户管理页面
@data_router.post("/users")
async def users(request: Request, current_user = Depends(get_current_user)):
    Users = WebAction().get_users().get("result")
    TopMenus = WebAction().get_top_menus().get("menus")

    return response(data=
        {
            "Users": Users,
            "UserCount": len(Users),
            "TopMenus": TopMenus,
        })


# 过滤规则设置页面
@data_router.post("/filterrule")
async def filterrule(request: Request, current_user = Depends(get_current_user)):
    result = WebAction().get_filterrules()

    return response(data=
        {
            "Count": len(result.get("ruleGroups")),
            "RuleGroups": result.get("ruleGroups"),
            "Init_RuleGroups": result.get("initRules"),
        })


# 目录同步页面
@data_router.post("/directorysync")
async def directorysync(request: Request, current_user = Depends(get_current_user)):
    from app.sync import Sync
    RmtModeDict = WebAction().get_rmt_modes()
    SyncPaths = Sync().get_sync_path_conf()
    return response(data=
        {
            "SyncPaths": SyncPaths,
            "SyncCount": len(SyncPaths),
            "RmtModeDict": RmtModeDict,
        })


# 自定义识别词设置页面
@data_router.post("/customwords")
async def customwords(request: Request, current_user = Depends(get_current_user)):
    groups = WebAction().get_customwords().get("result")
    return response(data=
        {
            "Groups": groups,
            "GroupsCount": len(groups),
        })


# 插件页面
@data_router.post("/plugin")
async def plugin(request: Request, current_user = Depends(get_current_user)):
    # 下载器
    DefaultDownloader = Downloader().default_downloader_id
    Downloaders = Downloader().get_downloader_conf()
    DownloadersCount = len(Downloaders)
    Categories = {
        x: WebAction().get_categories({
            "type": x
        }).get("category") for x in ["电影", "电视剧", "动漫"]
    }
    RmtModeDict = WebAction().get_rmt_modes()
    # 插件
    # 确保current_user不为None
    if current_user:
        # 直接传递用户级别
        from app.plugins.plugin_manager import PluginManager
        Plugins = PluginManager().get_plugins_conf(current_user.level)
    else:
        # 如果current_user为None，使用默认admin用户级别
        admin_user = UserManager().get_user_by_name("admin")
        if admin_user:
            # 直接传递admin用户级别
            from app.plugins.plugin_manager import PluginManager
            Plugins = PluginManager().get_plugins_conf(admin_user.level)
        else:
            # 如果无法获取admin用户，使用最高级别99
            from app.plugins.plugin_manager import PluginManager
            Plugins = PluginManager().get_plugins_conf(99)

    Settings = '\n'.join(SystemConfig().get(SystemConfigKey.ExternalPluginsSource) or [])
    return response(data=
        {
            "Config": Config().get_config(),
            "Downloaders": Downloaders,
            "DefaultDownloader": DefaultDownloader,
            "DownloadersCount": DownloadersCount,
            "Categories": Categories,
            "RmtModeDict": RmtModeDict,
            "DownloaderConf": ModuleConf.DOWNLOADER_CONF,
            "Plugins": Plugins,
            "Settings": Settings,
            "PluginCount": len(Plugins),
        })



# 排行榜页面
@data_router.post("/ranking")
async def ranking(request: Request, current_user: User = Depends(get_current_user)):
    """
    排行榜页面
    """
    return response(data=
        {
            "DiscoveryType": "RANKING",
        }
    )


# 电视剧排行榜页面
@data_router.post("/tv_ranking")
async def tv_ranking(request: Request, current_user: User = Depends(get_current_user)):
    """
    电视剧排行榜页面
    """
    return response(data=
        {
            "DiscoveryType": "TV",
        }
    )


# 用户RSS页面
@data_router.post("/user_rss")
async def user_rss(request: Request, current_user: User = Depends(get_current_user)):
    """
    用户RSS页面
    """
    Tasks = RssChecker().get_rsstask_info()
    RssParsers = RssChecker().get_userrss_parser()
    RuleGroups = {str(group["id"]): group["name"] for group in Filter().get_rule_groups()}
    DownloadSettings = {did: attr["name"] for did, attr in Downloader().get_download_setting().items()}
    RestypeDict = ModuleConf.TORRENT_SEARCH_PARAMS.get("restype")
    PixDict = ModuleConf.TORRENT_SEARCH_PARAMS.get("pix")

    return response(data=
        {
            "Tasks": Tasks,
            "Count": len(Tasks),
            "RssParsers": RssParsers,
            "RuleGroups": RuleGroups,
            "RestypeDict": RestypeDict,
            "PixDict": PixDict,
            "DownloadSettings": DownloadSettings,
        }
    )


# 服务页面
@data_router.post("/service")
async def service(request: Request, current_user: User = Depends(get_current_user)):
    """
    服务页面
    """
    # 所有规则组
    RuleGroups = Filter().get_rule_groups()
    # 所有同步目录
    SyncPaths = Sync().get_sync_path_conf()

    # 获取用户服务（get_current_user_required已经保证current_user不为空）
    Services = current_user.get_services()
    pt = Config().get_config('pt')
    # RSS订阅
    if "rssdownload" in Services:
        pt_check_interval = pt.get('pt_check_interval')
        if str(pt_check_interval).isdigit():
            tim_rssdownload = str(round(int(pt_check_interval) / 60)) + " 分钟"
            rss_state = 'ON'
        else:
            tim_rssdownload = ""
            rss_state = 'OFF'
        Services['rssdownload'].update({
            'time': tim_rssdownload,
            'state': rss_state,
        })

    # RSS搜索
    if "subscribe_search_all" in Services:
        search_rss_interval = pt.get('search_rss_interval')
        if str(search_rss_interval).isdigit():
            if int(search_rss_interval) < 3:
                search_rss_interval = 3
            tim_rsssearch = str(int(search_rss_interval)) + " 小时"
            rss_search_state = 'ON'
        else:
            tim_rsssearch = ""
            rss_search_state = 'OFF'
        Services['subscribe_search_all'].update({
            'time': tim_rsssearch,
            'state': rss_search_state,
        })

    # 下载文件转移
    if "pttransfer" in Services:
        pt_monitor = Downloader().monitor_downloader_ids
        if pt_monitor:
            tim_pttransfer = str(round(PT_TRANSFER_INTERVAL / 60)) + " 分钟"
            sta_pttransfer = 'ON'
        else:
            tim_pttransfer = ""
            sta_pttransfer = 'OFF'
        Services['pttransfer'].update({
            'time': tim_pttransfer,
            'state': sta_pttransfer,
        })

    # 目录同步
    if "sync" in Services:
        if Sync().monitor_sync_path_ids:
            Services['sync'].update({'state': 'ON'})

    # 系统进程
    if "processes" in Services:
        if not SystemUtils.is_docker() or not SystemUtils.get_all_processes():
            Services.pop('processes')

    return response(data=
        {
            "Count": len(Services),
            "RuleGroups": RuleGroups,
            "SyncPaths": SyncPaths,
            "SchedulerTasks": Services,
        }
    )


# 正在下载页面
@data_router.post("/downloading")
async def downloading(request: Request, current_user: User = Depends(get_current_user)):
    """
    正在下载页面
    """
    downloader_proxy = Downloader()
    Downloaders = []
    for key, value in downloader_proxy.get_downloader_conf().items():
        if value.get('enabled'):
            Downloaders.append(value)

    did = request.query_params.get('downloaderId')
    if not did:
        did = downloader_proxy.default_downloader_id

    DispTorrents = WebAction().get_downloading(downloader_id=did).get("result")

    return response(data=
        {
            "DownloaderId": int(did) if did else 0,
            "Downloaders": Downloaders,
            "DownloadCount": len(DispTorrents),
            "Torrents": DispTorrents,
        }
    )


# 媒体文件管理页面
@data_router.post("/mediafile")
async def mediafile(request: Request, current_user: User = Depends(get_current_user)):
    """
    媒体文件管理页面
    """
    media_default_path = Config().get_config('media').get('media_default_path')
    if media_default_path:
        DirD = media_default_path
    else:
        download_dirs = Downloader().get_download_visit_dirs()
        if download_dirs:
            try:
                DirD = os.path.commonpath(download_dirs).replace("\\", "/")
            except Exception as err:
                log.exception(f'管理目录转换异常: {download_dirs}', err)
                DirD = "/"
        else:
            DirD = "/"
    DirR = request.query_params.get("dir")

    return response(data=
        {
            "Dir": DirR or DirD,
        }
    )


# 数据统计页面
@data_router.post("/statistics")
async def statistics(request: Request, current_user: User = Depends(get_current_user)):
    """
    数据统计页面
    """
    # 刷新单个site
    refresh_site = request.query_params.getlist("refresh_site")
    # 强制刷新所有
    refresh_force = True if request.query_params.get("refresh_force") else False
    # 总上传下载
    TotalUpload = 0
    TotalDownload = 0
    TotalSeedingSize = 0
    TotalSeeding = 0
    # 站点标签及上传下载
    SiteNames = []
    SiteUploads = []
    SiteDownloads = []
    SiteRatios = []
    SiteErrs = {}
    # 站点上传下载
    SiteData = SiteUserInfo().get_site_data(specify_sites=refresh_site, force=refresh_force)
    if isinstance(SiteData, dict):
        for name, data in SiteData.items():
            if not data:
                continue
            up = data.get("upload", 0)
            dl = data.get("download", 0)
            ratio = data.get("ratio", 0)
            seeding = data.get("seeding", 0)
            seeding_size = data.get("seeding_size", 0)
            err_msg = data.get("err_msg", "")

            SiteErrs.update({name: err_msg})

            if not up and not dl and not ratio:
                continue
            if not str(up).isdigit() or not str(dl).isdigit():
                continue
            if name not in SiteNames:
                SiteNames.append(name)
                TotalUpload += int(up)
                TotalDownload += int(dl)
                TotalSeeding += int(seeding)
                TotalSeedingSize += int(seeding_size)
                SiteUploads.append(int(up))
                SiteDownloads.append(int(dl))
                SiteRatios.append(round(float(ratio), 1))

    # 站点用户数据
    SiteUserStatistics = WebAction().get_site_user_statistics({"encoding": "DICT"}).get("data")

    return response(data=
        {
            "TotalDownload": TotalDownload,
            "TotalUpload": TotalUpload,
            "TotalSeedingSize": TotalSeedingSize,
            "TotalSeeding": TotalSeeding,
            "SiteDownloads": SiteDownloads,
            "SiteUploads": SiteUploads,
            "SiteRatios": SiteRatios,
            "SiteNames": SiteNames,
            "SiteErr": SiteErrs,
            "SiteUserStatistics": SiteUserStatistics,
        }
    )


# 刷流任务页面
@data_router.post("/brushtask")
async def brushtask(request: Request, current_user: User = Depends(get_current_user)):
    """
    刷流任务页面
    """
    # 站点列表
    CfgSites = Sites().get_sites(brush=True)
    # 下载器列表
    Downloaders = Downloader().get_downloader_conf_simple()
    # 任务列表
    Tasks = BrushTask().get_brushtask_info()

    return response(data=
        {
            "Count": len(Tasks),
            "Sites": CfgSites,
            "Tasks": Tasks,
            "Downloaders": Downloaders,
        }
    )


# RSS解析器页面
@data_router.post("/rss_parser")
async def rss_parser(request: Request, current_user: User = Depends(get_current_user)):
    """
    RSS解析器页面
    """
    RssParsers = RssChecker().get_userrss_parser()

    return response(data=
        {
            "RssParsers": RssParsers,
            "Count": len(RssParsers),
        }
    )


# 自动删种页面
@data_router.post("/torrent_remove")
async def torrent_remove(request: Request, current_user: User = Depends(get_current_user)):
    """
    自动删种页面
    """
    Downloaders = Downloader().get_downloader_conf_simple()
    TorrentRemoveTasks = TorrentRemover().get_torrent_remove_tasks()

    return response(data=
        {
            "Downloaders": Downloaders,
            "DownloaderConfig": ModuleConf.TORRENTREMOVER_DICT,
            "Count": len(TorrentRemoveTasks),
            "TorrentRemoveTasks": TorrentRemoveTasks,
        }
    )


# 近期下载页面
@data_router.post("/downloaded")
async def downloaded(request: Request, current_user: User = Depends(get_current_user)):
    """
    近期下载页面
    """
    CurrentPage = request.query_params.get("page") or 1

    return response(data=
        {
            "Type": 'DOWNLOADED',
            "Title": '近期下载',
            "CurrentPage": CurrentPage,
        }
    )


# 下载设置页面
@data_router.post("/download_setting")
async def download_setting(request: Request, current_user: User = Depends(get_current_user)):
    """
    下载设置页面
    """
    DefaultDownloadSetting = Downloader().default_download_setting_id
    Downloaders = Downloader().get_downloader_conf_simple()
    DownloadSetting = Downloader().get_download_setting()

    return response(data=
        {
            "DownloadSetting": DownloadSetting,
            "DefaultDownloadSetting": DefaultDownloadSetting,
            "Downloaders": Downloaders,
            "Count": len(DownloadSetting),
        }
    )


# 推荐页面
@data_router.post("/recommend")
async def recommend(request: Request, current_user: User = Depends(get_current_user)):
    """
    推荐页面
    """
    Type = request.query_params.get("type") or ""
    SubType = request.query_params.get("subtype") or ""
    Title = request.query_params.get("title") or ""
    SubTitle = request.query_params.get("subtitle") or ""
    CurrentPage = request.query_params.get("page") or 1
    Week = request.query_params.get("week") or ""
    TmdbId = request.query_params.get("tmdbid") or ""
    PersonId = request.query_params.get("personid") or ""
    Keyword = request.query_params.get("keyword") or ""
    Source = request.query_params.get("source") or ""
    FilterKey = request.query_params.get("filter") or ""
    Params = json.loads(request.query_params.get("params")) if request.query_params.get("params") else {}

    return response(data=
        {
            "Type": Type,
            "SubType": SubType,
            "Title": Title,
            "CurrentPage": CurrentPage,
            "Week": Week,
            "TmdbId": TmdbId,
            "PersonId": PersonId,
            "SubTitle": SubTitle,
            "Keyword": Keyword,
            "Source": Source,
            "Filter": FilterKey,
            "FilterConf": ModuleConf.DISCOVER_FILTER_CONF.get(FilterKey) if FilterKey else {},
            "Params": Params,
        }
    )


# 豆瓣电影
@data_router.post("/douban_movie")
async def douban_movie(request: Request, current_user: User = Depends(get_current_user)):
    """
    豆瓣电影页面
    """
    return response(data=
        {
            "Type": "DOUBANTAG",
            "SubType": "MOV",
            "Title": "豆瓣电影",
            "Filter": "douban_movie",
            "FilterConf": ModuleConf.DISCOVER_FILTER_CONF.get('douban_movie'),
        }
    )


# 豆瓣电视剧
@data_router.post("/douban_tv")
async def douban_tv(request: Request, current_user: User = Depends(get_current_user)):
    """
    豆瓣电视剧页面
    """
    return response(data=
        {
            "Type": "DOUBANTAG",
            "SubType": "TV",
            "Title": "豆瓣剧集",
            "Filter": "douban_tv",
            "FilterConf": ModuleConf.DISCOVER_FILTER_CONF.get('douban_tv'),
        }
    )


# TMDB电影
@data_router.post("/tmdb_movie")
async def tmdb_movie(request: Request, current_user: User = Depends(get_current_user)):
    """
    TMDB电影页面
    """
    return response(data=
        {
            "Type": "DISCOVER",
            "SubType": "MOV",
            "Title": "TMDB电影",
            "Filter": "tmdb_movie",
            "FilterConf": ModuleConf.DISCOVER_FILTER_CONF.get('tmdb_movie'),
        }
    )


# TMDB电视剧
@data_router.post("/tmdb_tv")
async def tmdb_tv(request: Request, current_user: User = Depends(get_current_user)):
    """
    TMDB电视剧页面
    """
    return response(data=
        {
            "Type": "DISCOVER",
            "SubType": "TV",
            "Title": "TMDB剧集",
            "Filter": "tmdb_tv",
            "FilterConf": ModuleConf.DISCOVER_FILTER_CONF.get('tmdb_tv'),
        }
    )


# Bangumi每日放送
@data_router.post("/bangumi")
async def bangumi(request: Request, current_user: User = Depends(get_current_user)):
    """
    Bangumi每日放送页面
    """
    return response(data=
        {
            "DiscoveryType": "BANGUMI",
        }
    )


# 媒体详情页面
@data_router.post("/media_detail")
async def media_detail(request: Request, current_user: User = Depends(get_current_user)):
    """
    媒体详情页面
    """
    TmdbId = request.query_params.get("id")
    Type = request.query_params.get("type")

    return response(data=
        {
            "TmdbId": TmdbId,
            "Type": Type,
        }
    )


# 演职人员页面
@data_router.post("/discovery_person")
async def discovery_person(request: Request, current_user: User = Depends(get_current_user)):
    """
    演职人员页面
    """
    TmdbId = request.query_params.get("tmdbid")
    Title = request.query_params.get("title")
    SubTitle = request.query_params.get("subtitle")
    Type = request.query_params.get("type")
    Keyword = request.query_params.get("keyword")

    return response(data=
        {
            "TmdbId": TmdbId,
            "Title": Title,
            "SubTitle": SubTitle,
            "Type": Type,
            "Keyword": Keyword,
        }
    )


# TMDB缓存页面
@data_router.post("/tmdbcache")
async def tmdbcache(request: Request, current_user: User = Depends(get_current_user)):
    """
    TMDB缓存页面
    """
    page_num = request.query_params.get("pagenum")
    if not page_num:
        page_num = 30
    search_str = request.query_params.get("s")
    if not search_str:
        search_str = ""
    current_page = request.query_params.get("page")
    if not current_page:
        current_page = 1
    else:
        current_page = int(current_page)

    total_count, tmdb_caches = MetaHelper().dump_meta_data(search_str, current_page, page_num)
    total_page = floor(total_count / page_num) + 1
    page_range = WebUtils.get_page_range(current_page=current_page, total_page=total_page)

    return response(data=
        {
            "TmdbCaches": tmdb_caches,
            "Search": search_str,
            "CurrentPage": current_page,
            "TotalCount": total_count,
            "TotalPage": total_page,
            "PageRange": page_range,
            "PageNum": page_num,
        }
    )


# 系统设置页面
@data_router.post("/system")
async def system(request: Request, current_user: User = Depends(get_current_user)):
    """
    系统设置页面
    """
    SystemConfig = Config().get_config('system') or {}
    CurrentVersion = WebUtils.get_current_version()
    RestartFlag = SystemConfig.get("restart_flag") or ""
    return response(data=
        {
            "Config": Config().get_config(),
            "CurrentVersion": CurrentVersion,
            "RestartFlag": RestartFlag,
            "IndexerStatistics": WebAction().get_indexer_statistics().get("result"),
        }
    )


# 实验室页面
@data_router.post("/laboratory")
async def laboratory(request: Request, current_user: User = Depends(get_current_user)):
    """
    实验室页面
    """
    return response(data=
        {
            "Config": Config().get_config(),
        }
    )

# 字幕页面
@data_router.post("/subtitle")
async def subtitle(request: Request, current_user: User = Depends(get_current_user)):
    """
    字幕页面
    """
    SubtitleSites = SystemConfig().get(SystemConfigKey.UserSubtitleSites) or []
    return response(data=
        {
            "SubtitleSites": SubtitleSites,
        }
    )


# 历史记录页面
@data_router.post("/history")
async def history(request: Request, current_user: User = Depends(get_current_user)):
    """
    历史记录页面
    """
    pagenum = request.query_params.get("pagenum")
    keyword = request.query_params.get("s") or ""
    current_page = request.query_params.get("page")
    Result = WebAction().get_transfer_history({"keyword": keyword, "page": current_page, "pagenum": pagenum})
    PageRange = WebUtils.get_page_range(current_page=Result.get("currentPage"),
                                        total_page=Result.get("totalPage"))

    return response(data=
        {
            "TotalCount": Result.get("total"),
            "Count": len(Result.get("result")),
            "Historys": Result.get("result"),
            "Search": keyword,
            "CurrentPage": Result.get("currentPage"),
            "TotalPage": Result.get("totalPage"),
            "PageRange": PageRange,
            "PageNum": Result.get("currentPage"),
        }
    )


# 手工识别页面
@data_router.post("/unidentification")
async def unidentification(request: Request, current_user: User = Depends(get_current_user)):
    """
    手工识别页面
    """
    pagenum = request.query_params.get("pagenum")
    keyword = request.query_params.get("s") or ""
    current_page = request.query_params.get("page")
    Result = WebAction().get_unknown_list_by_page({"keyword": keyword, "page": current_page, "pagenum": pagenum})
    PageRange = WebUtils.get_page_range(current_page=Result.get("currentPage"),
                                        total_page=Result.get("totalPage"))

    return response(data=
        {
            "TotalCount": Result.get("total"),
            "Count": len(Result.get("items")),
            "Items": Result.get("items"),
            "Search": keyword,
            "CurrentPage": Result.get("currentPage"),
            "TotalPage": Result.get("totalPage"),
            "PageRange": PageRange,
            "PageNum": Result.get("currentPage"),
        }
    )
