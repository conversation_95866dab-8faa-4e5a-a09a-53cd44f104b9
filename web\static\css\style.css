html {
    --safe-area-inset-top: 0px;
    min-height: calc(100% + env(safe-area-inset-top) + var(--safe-area-inset-top))
}

body, .page {
    min-height: 100%;
}

body {
    overflow-y: hidden;  /* 禁止水平滚动条 */
    margin: 0;
    padding: 0;
    background-size: cover;       /* 背景图等比缩放，完全覆盖整个屏幕 */
    background-position: center;  /* 居中显示 */
    background-repeat: no-repeat; /* 不重复 */
    position: relative;
}

/* 半透明遮罩 */
body::before {
  content: "";
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(255,255,255,0.2);  /* 半透明白色蒙层 */
  backdrop-filter: blur(10px);        /* 毛玻璃模糊 */
  -webkit-backdrop-filter: blur(10px);/* Safari 支持 */
  z-index: -1;
}

.tooltip-inner {
    text-align: left;
}

.fileTree {
    width: 240px;
    max-height: 200px;
    overflow-y: scroll;
    overflow-x: hidden;
    position: absolute;
    display: none;
}

.dropzone {
    border: 1px dashed var(--tblr-border-color) !important;
}


@media (max-width: 992px) {
    .navbar ul.navbar-nav {
        overflow-y: auto;
        max-height: 80vh;
    }
    .lit-navbar {
        max-height: 0 !important;
        min-height: 0 !important;
        padding: 0 !important;
        margin: 0 !important;
    }
    .custom-border-top {
        border-top: 1px var(--tblr-border-style) rgba(97, 104, 118, .16) !important;
    }
}

.page-wrapper {
    padding-top: calc(env(safe-area-inset-top) + var(--safe-area-inset-top) + 51px) !important;
    padding-left: env(safe-area-inset-left) !important;
    overflow: hidden !important;
}

.page-wrapper-top-off {
    margin-top: calc(0px - env(safe-area-inset-top) - var(--safe-area-inset-top) - 51px) !important;
}

#navbar-menu {
    box-shadow: none !important;
    border: 0 !important;
}

#logo_animation {
    position:absolute;
    top:30%;
    left:calc(50% - 40px);
    width:80px;
    height:80px;
    z-index: 1033;
}

.offcanvas {
    padding-top: calc(env(safe-area-inset-top) + var(--safe-area-inset-top)) !important;
    padding-left: env(safe-area-inset-left) !important;
}

.modal-dialog {
    padding-top: calc(env(safe-area-inset-top) + var(--safe-area-inset-top)) !important;
}


.fc-toolbar-title {
    font-size: 1.5em !important;
}

.fc-list-event .media_calendar_item_info {
    display: block !important;
}

.fc .fc-list-event:hover td {
    background-color: rgba(200, 200, 200, 0.1) !important;
}

.lit-normal-card {
    position: relative;
    z-index: 1;
    --tblr-aspect-ratio: 150%;
    border: none;
}

.lit-normal-card:hover {
    transform: scale(1.05, 1.05);
    opacity: 1;
}

.lit-media-info-background {
    background-image: linear-gradient(180deg, rgba(var(--tblr-body-bg-rgb), 0) 50%, rgba(var(--tblr-body-bg-rgb), 1) 100%),
    linear-gradient(90deg, rgba(var(--tblr-body-bg-rgb), 0) 50%, rgba(var(--tblr-body-bg-rgb), 1) 100%),
    linear-gradient(270deg, rgba(var(--tblr-body-bg-rgb), 0) 50%, rgba(var(--tblr-body-bg-rgb), 1) 100%);
    box-shadow: 0 0 0 2px rgb(var(--tblr-body-bg-rgb));
}

.theme-light .lit-media-info-background {
    background-image: linear-gradient(180deg, rgba(231, 235, 239, 0) 50%, rgba(231, 235, 239, 1) 100%),
    linear-gradient(90deg, rgba(231, 235, 239, 0) 50%, rgba(231, 235, 239, 1) 100%),
    linear-gradient(270deg, rgba(231, 235, 239, 0) 50%, rgba(231, 235, 239, 1) 100%);
    box-shadow: 0 0 0 2px rgb(231, 235, 239);
}

.lit-media-info-image {
    width: 233px;
    height: 350px;
}

.custom-media-info-height {
    height: calc(env(safe-area-inset-top) + var(--safe-area-inset-top) + 541px);
    border: none;
}

@media (max-width: 768px) {
    .lit-media-info-image {
        width: 150px;
        height: 225px;
    }
    .div-media-detail-margin {
        margin-left: unset !important;
        margin-right: unset !important;
    }
    .custom-media-info-height {
        height: calc(env(safe-area-inset-top) + var(--safe-area-inset-top) + 610px);
        border: none;
    }
    #litLayoutNavbar{
        padding-bottom: calc(env(safe-area-inset-bottom) + 61px);
    }
    #page_content {
        padding-bottom: calc(env(safe-area-inset-bottom) + 61px);
    }
}

.lit-person-card {
    position: relative;
    z-index: 1;
    --tblr-aspect-ratio: 150%;
    border: none;
    box-shadow: 0 0 0 1px #888888 inset, 0 .125rem .25rem rgba(0, 0, 0, 0.2);
    background-image: linear-gradient(45deg, #99999b, #637599 60%);
}

.lit-person-card:hover {
    transform: scale(1.05, 1.05);
    opacity: 1;
    box-shadow: 0 0 0 1px #bbbbbb inset;
    background-image: linear-gradient(45deg, #bbbbbd, #8597aa 60%);
}

.grid-normal-card {
    grid-template-columns: repeat(auto-fill, minmax(15rem, 1fr));
}

.grid-media-card {
    grid-template-columns: repeat(auto-fill, minmax(9.375rem, 1fr));
}

.grid-info-card {
    grid-template-columns: repeat(auto-fill, minmax(20rem, 1fr));
}

.grid-large-card {
    grid-template-columns: repeat(auto-fill, minmax(24rem, 1fr));
}

.offcanvas-backdrop.show {
    opacity: 0.5 !important;
    background-color: #000 !important;
}

.select_logger {
    display: flex;
    align-items: center;
}

.select_logger h5 {
    padding-right: 5px
}

.table-modal-body {
    max-height: 40rem;
    min-height: 300px;
    overflow-y: auto;
}

.table-page-body {
    min-height: 20rem;
}

.table-page-mini-body {
    min-height: 15rem;
}

.div-media-detail-margin {
    margin-left: 2rem !important;
    margin-right: 2rem !important;
}

.chart-title {
    display: inline-block;
    text-align: center;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}

.filter_releasegroup_span {
    max-width: 100px;
    display: inline-block;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}

.scroll-top-btn {
    opacity: 0.8;
    position: fixed;
    bottom: 20%;
    right: 5%;
    z-index: 2;
    display: none;
}

.scroll-bottom-btn {
    opacity: 0.8;
    position: fixed;
    bottom: 10%;
    right: 5%;
    z-index: 2;
    display: none;
}

.media-slide-hide-scrollbar {
    overflow-x: scroll !important;
    overscroll-behavior-x: contain !important;
    scrollbar-width: none !important;
    -ms-overflow-style: none !important;
}

.media-slide-hide-scrollbar::-webkit-scrollbar {
    display: none;
}

.media-slide-card-number {
    position: relative;
    flex: 0 0 auto;
    max-width: 11rem;
}

.background-blur {
    backdrop-filter: blur(8px);
    -webkit-backdrop-filter: blur(8px);
}

.lit-searchbar {
    background-color: rgba(0, 0, 0, 0) !important;
    border-right: none !important;
    box-shadow: none !important;
}

.lit-searchbar-blur {
    backdrop-filter: blur(8px);
    -webkit-backdrop-filter: blur(8px);
}

.theme-dark .lit-searchbar-blur {
    background-color: rgba(29, 39, 59, 0.6) !important;
}

.theme-light .lit-searchbar-blur {
    background-color: rgba(231, 235, 239, 0.7) !important;
}

.navbar .input-group-flat:focus-within {
    box-shadow: none;
}

.nav-search-bar {
    padding-top: calc(env(safe-area-inset-top) + var(--safe-area-inset-top)) !important;
    padding-left: env(safe-area-inset-left) !important;
}

.lit-navar-close {
    margin-top: calc(env(safe-area-inset-top) + var(--safe-area-inset-top)) !important;
}

.lit-navbar-fixed {
    position: fixed;
    top: 0;
    left: 0;
    z-index: 1031
}

.lit-navbar-canvas {
    width: calc(var(--tblr-offcanvas-width) - 120px) !important;
}

.theme-light .lit-navbar-canvas {
    background-color: rgb(231, 235, 239);
}

.lit-navar-close {
    position: fixed;
    top: 0;
    left: calc(var(--tblr-offcanvas-width) - 120px);
    z-index: var(--tblr-offcanvas-zindex);
    width: 80px;
}

.lit-navbar-hide-scrollbar {
    overflow-y: scroll !important;
    scrollbar-width: none !important;
    -ms-overflow-style: none !important;
}

.lit-navbar-hide-scrollbar::-webkit-scrollbar {
    display: none;
}

.lit-navbar-nav {
    max-height: none !important;
}

.theme-light .lit-navbar {
    background-color: rgb(231, 235, 239, 0.5);
}

.lit-navbar-logo {
    height: 3rem;
    width: auto;
}

.theme-dark .lit-navbar-logo {
    filter: invert(1) grayscale(100%) brightness(200%);
}

.lit-navbar-accordion-item, .lit-navbar-accordion-item-active {
    border-radius: 0.5rem;
}

.theme-dark .lit-navbar-accordion-item:hover {
    background-color: #2a3551ca !important;
}

.theme-light .lit-navbar-accordion-item:hover {
    background-color: #fcfafec5 !important;
}

.theme-dark .lit-navbar-accordion-item-active {
    background-color: #414d6dca !important;
}

.theme-light .lit-navbar-accordion-item-active {
    background-color: rgba(123, 178, 233, 0.5) !important;
    color: #000 !important;
}

.lit-custom-img-carousel {
    opacity: 0;
    transition: 1s;
}

.lit-custom-img-carousel-show {
    opacity: 1;
}

.lit-media-info-page-bg {
    background-color: rgb(var(--tblr-body-bg-rgb));
}

.theme-light .lit-media-info-page-bg {
    background-color: rgb(231, 235, 239);
}

.main-bottom-menubar {
    backdrop-filter: blur(8px);
    -webkit-backdrop-filter: blur(8px);
    z-index: 1032;
    border-top: 1px solid var(--tblr-border-color);
    bottom: 0;
    padding-bottom: calc(env(safe-area-inset-bottom));
}

.theme-dark .main-bottom-menubar {
    background-color: rgba(29, 39, 59, 0.6) !important;
}

.theme-light .main-bottom-menubar {
    background-color: rgba(231, 235, 239, 0.7) !important;
}

.main-bottom-menubar svg {
    width: 28px;
    height: 28px;
}


.top-sub-navbar {
    font-size: 1rem;
    border-bottom: 0;
  }

  div.top-sub-navbar>ul {
    display: flex;
    flex-wrap: nowrap;
    border-bottom: 1px solid rgba(231, 227, 252, 0.4);
    background: transparent;
    justify-content: flex-start;
  }

  div.top-sub-navbar>ul>li {
    min-width: 9.5rem;
  }

  div.top-sub-navbar>ul>li>a {
    display: block;
    text-align: center;
  }

  div.top-sub-navbar>ul>li>a.active {
    display: block;
    box-shadow: inset 0 -1px 0 0 var(--tblr-link-hover-color);
    /* 模拟边框效果 */
  }

  /* 手机屏幕上的样式 */
  @media (max-width: 576px) {
    div.top-sub-navbar>ul {
      justify-content: center;
      /* 手机模式下居中对齐 */
      padding: 0;
      /* 去掉左右的空白 */
      overflow-x: hidden;
    }

    div.top-sub-navbar>ul>li {
      margin-left: 0;
      /* 移除间距 */
      min-width: auto;
      /* 移除最小宽度限制，标签宽度自适应 */
      flex: 1;
      /* 让每个标签等宽，填满整个行 */
    }

    div.top-sub-navbar>ul>li>a {
      margin: 0 !important;
    }
  }


  .scroll-to-top {
    position: fixed;
    bottom: 20px;
    right: 20px;
    background-color: #007bff;
    color: white;
    border: none;
    border-radius: 50%;
    width: 50px;
    height: 50px;
    display: none;
    align-items: center;
    justify-content: center;
    font-size: 24px;
    cursor: pointer;
    z-index: 1000;
    opacity: 0;
    /* transition: background-color 0.3s, transform 0.3s; */
    transition: opacity 0.3s ease-in-out;
    pointer-events: none;
  }

  .scroll-to-top.show {
    display: flex;
    opacity: 1;
    pointer-events: auto;
  }

  .scroll-to-top:hover {
    background-color: #0056b3;
    /* 鼠标悬停背景颜色 */
    transform: scale(1.1);
    /* 鼠标悬停时放大 */
  }

  .nav-tabs .nav-link.active {
    border-color: transparent;
    border-bottom-color: var(--tblr-link-hover-color);
  }

  .components-div {
    border-radius: 5px;
    border: var(--tblr-card-border-width) solid var(--tblr-card-border-color);
    background-color: var(--tblr-card-bg);
    background-clip: border-box;
    margin: 10px;
  }

  .nav-link-icon {
    margin: 0 .6rem;
  }

  .nav-link {
    margin-right: 1rem !important;
  }

  .section-title-container {
    display: flex;
    align-items: center;
    margin: .5rem 1rem;
  }

  .section-title {
    color: var(--tblr-card-border-color);
    font-size: 0.9rem;
    text-transform: uppercase;
    margin-right: 10px;
    white-space: nowrap;
    border-radius: 4px;
  }

  .divider-line {
    flex-grow: 1;
    height: 1px;
    background-color: rgba(231, 227, 252,0.2)
  }

  .nav-link-title {
    margin-left: 0.5rem;
  }

  .space-between-box {
    display: flex;
    justify-content: space-between;
    margin: 0.25rem;
  }

  .space-between-box a:hover {
    text-decoration: none;
  }

  .media-box {
    position: relative;
    background-size: cover;
    background-position: center;
    background-repeat: no-repeat;
    border-radius: var(--tblr-card-border-radius);
  }

  .media-box::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(to right, rgba(0, 0, 0, 0.5) 0%, var(--tblr-dark) 100%);
    z-index: 0;
    border-radius: var(--tblr-card-border-radius);
  }

  .media-content {
    color: white;
    position: relative;
    z-index: 1;
  }

  .card-text {
    font-weight: 500 !important;
    text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.7);
  }

  /* 加载框 */
  .wave-loading {
    display: none; /* 默认隐藏 */
    position: fixed;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 100%;
    height: 400px;
    z-index: 9999;
    display: flex;
    justify-content: center;
    align-items: center;
}
/* 小竖条 */
.wave-loading-item {
    height: 50px;
    width: 5px;
    background: var(--tblr-body-color);
    /* 加margin，使竖条之间有空隙 */
    margin: 0px 3px;
    /* 圆角 */
    border-radius: 10px;
    /* 动画：名称、时间、循环 */
    animation: loading 1s infinite;
}

/* 设置动画 */
@keyframes loading {
    0% {
        height: 0px;
    }
    50% {
        height: 50px;
    }
    100% {
        height: 0px;
    }
}

/* 为每一个竖条设置延时 */
.wave-loading-item:nth-child(2) {
    animation-delay: 0.1s;
}

.wave-loading-item:nth-child(3) {
    animation-delay: 0.2s;
}

.wave-loading-item:nth-child(4) {
    animation-delay: 0.3s;
}

.wave-loading-item:nth-child(5) {
    animation-delay: 0.4s;
}

.wave-loading-item:nth-child(6) {
    animation-delay: 0.5s;
}

.wave-loading-item:nth-child(7) {
    animation-delay: 0.6s;
}

.wave-loading-item:nth-child(8) {
    animation-delay: 0.7s;
}

.top-nav-link {
    display: flex;
    align-items: center; /* 垂直居中对齐 */
    justify-content: center; /* 水平居中（可选，具体场景调整） */
    gap: 6px; /* 图标和文字的间距 */
    padding: 8px 12px; /* 增加触控区域 */
    height: 48px; /* 固定高度 */
  }
  
  .tab-icon {
    display: inline-block; /* 保证 SVG 图标的布局独立 */
    width: 24px;
    height: 24px;
    vertical-align: middle; /* 修复 SVG 默认基线偏移 */
  }
  
  .tab-text {
    display: inline-block; /* 保证文字布局独立 */
    max-width: 120px; /* 限制文字最大宽度 */
    white-space: nowrap; /* 禁止文字换行 */
    overflow: hidden; /* 超出隐藏 */
    text-overflow: ellipsis; /* 超出部分显示省略号 */
    vertical-align: middle; /* 确保与图标保持一致 */
    line-height: 1; /* 避免文字行高影响对齐 */
    margin: 0 0.1rem;
  }
  

  .right-top-box {
    position: absolute;
    top: 1rem;
    right: 1.5rem;
    z-index: 99;
  }
  
  .tiny-top-left {
    position: absolute;
    left: 5px;
    top: 5px;
    font-size: xx-small;
  }

  .download-speed-indicator {
    padding: 8px 0;
  }
  
  .txt-download-speed {
    color: greenyellow;
    font-weight: 600;
    padding: 0 5px;
  }
  
  .txt-upload-speed {
    color: #ffa726;
    font-weight: 600;
    padding: 0 5px;
  }

  .cover-container img {
      width: 100%;
      height: 100%;
      object-fit: cover;
      display: block;
  }

  a:hover {
    text-decoration: none;
  }

  /* 自定义图标 jellyfin */
  .ti.ti-jellyfin::before {
    content: "";
    display: inline-block;
    width: 1em;
    height: 1em;
    background-color: currentColor;
    -webkit-mask: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24'%3E%3Cg %3E%3Cpath d='M12.001 9.663c-.877 0-3.704 5.127-3.275 5.99.43.865 6.125.856 6.55 0 .425-.854-2.394-5.99-3.275-5.99z'/%3E%3Cpath d='M12.001 2C9.354 2 .836 17.446 2.134 20.055c1.298 2.608 18.45 2.578 19.735 0C23.154 17.476 14.649 2 12 2Zm6.468 15.794c-.842 1.69-12.08 1.71-12.932 0-.85-1.71 4.732-11.832 6.464-11.832 1.732 0 7.31 10.139 6.468 11.832z'/%3E%3C/g%3E%3C/svg%3E") no-repeat center / contain;
    mask: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24'%3E%3Cg %3E%3Cpath d='M12.001 9.663c-.877 0-3.704 5.127-3.275 5.99.43.865 6.125.856 6.55 0 .425-.854-2.394-5.99-3.275-5.99z'/%3E%3Cpath d='M12.001 2C9.354 2 .836 17.446 2.134 20.055c1.298 2.608 18.45 2.578 19.735 0C23.154 17.476 14.649 2 12 2Zm6.468 15.794c-.842 1.69-12.08 1.71-12.932 0-.85-1.71 4.732-11.832 6.464-11.832 1.732 0 7.31 10.139 6.468 11.832z'/%3E%3C/g%3E%3C/svg%3E") no-repeat center / contain;
  }