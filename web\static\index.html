<!doctype html>
<html lang="en">

<head>
  <meta charset="utf-8" />

  <meta name="viewport"
    content="width=device-width, initial-scale=1, maximum-scale=1, viewport-fit=cover, user-scalable=0">
  <meta name="Robots" content="noindex,nofollow,noarchive">

  <meta name="mobile-web-app-capable" content="yes" />
  <meta name="apple-mobile-web-app-capable" content="yes" />
  <meta name="apple-mobile-web-app-status-bar-style" content="black-translucent">
  <meta name="apple-mobile-web-app-title" content="NAStool">
  <meta name="description" content="NAS媒体库管理工具">
  <meta name="format-detection" content="telephone=no">
  <meta name="referrer" content="never">
  <meta name="msapplication-TileColor" content="#1e293b" />
  <meta name="theme-color" content="#f1f6fa" media="(prefers-color-scheme: dark)">
  <meta name="theme-color" content="#1e293b" media="(prefers-color-scheme: light)">
  <meta name="HandheldFriendly" content="True" />
  <meta name="MobileOptimized" content="320" />

  <link rel="icon" type="image/png" href="/static/img/logo/logo.png">
  <link rel="icon" type="image/png" sizes="32x32" href="/static/img/logo/logo-32x32.png">
  <link rel="icon" type="image/png" sizes="16x16" href="/static/img/logo/logo-16x16.png">

  <link rel="apple-touch-icon" href="/static/img/logo/logo-black.png">
  <link rel="apple-touch-icon" sizes="128x128" href="/static/img/icons/128.png">
  <link rel="apple-touch-icon" sizes="144x144" href="/static/img/icons/144.png">
  <link rel="apple-touch-icon" sizes="152x152" href="/static/img/icons/152.png">
  <link rel="apple-touch-icon" sizes="167x167" href="/static/img/icons/167.png">
  <link rel="apple-touch-icon" sizes="172x172" href="/static/img/icons/172.png">
  <link rel="apple-touch-icon" sizes="180x180" href="/static/img/icons/180.png">
  <link rel="apple-touch-icon" sizes="196x196" href="/static/img/icons/196.png">
  <link rel="apple-touch-icon" sizes="216x216" href="/static/img/icons/216.png">
  <link rel="apple-touch-icon" sizes="256x256" href="/static/img/icons/256.png">
  <link rel="apple-touch-icon" sizes="512x512" href="/static/img/icons/512.png">
  <link rel="apple-touch-icon" sizes="1024x1024" href="/static/img/icons/1024.png">
  <link rel="apple-touch-startup-image" href="/static/img/startup.jpg">
  <link rel="apple-touch-startup-image" href="/static/img/splash/apple-splash-2048-2732.png"
    media="(device-width: 1024px) and (device-height: 1366px) and (-webkit-device-pixel-ratio: 2) and (orientation: portrait)">
  <link rel="apple-touch-startup-image" href="/static/img/splash/apple-splash-2732-2048.png"
    media="(device-width: 1024px) and (device-height: 1366px) and (-webkit-device-pixel-ratio: 2) and (orientation: landscape)">
  <link rel="apple-touch-startup-image" href="/static/img/splash/apple-splash-1668-2388.png"
    media="(device-width: 834px) and (device-height: 1194px) and (-webkit-device-pixel-ratio: 2) and (orientation: portrait)">
  <link rel="apple-touch-startup-image" href="/static/img/splash/apple-splash-2388-1668.png"
    media="(device-width: 834px) and (device-height: 1194px) and (-webkit-device-pixel-ratio: 2) and (orientation: landscape)">
  <link rel="apple-touch-startup-image" href="/static/img/splash/apple-splash-1536-2048.png"
    media="(device-width: 768px) and (device-height: 1024px) and (-webkit-device-pixel-ratio: 2) and (orientation: portrait)">
  <link rel="apple-touch-startup-image" href="/static/img/splash/apple-splash-2048-1536.png"
    media="(device-width: 768px) and (device-height: 1024px) and (-webkit-device-pixel-ratio: 2) and (orientation: landscape)">
  <link rel="apple-touch-startup-image" href="/static/img/splash/apple-splash-1668-2224.png"
    media="(device-width: 834px) and (device-height: 1112px) and (-webkit-device-pixel-ratio: 2) and (orientation: portrait)">
  <link rel="apple-touch-startup-image" href="/static/img/splash/apple-splash-2224-1668.png"
    media="(device-width: 834px) and (device-height: 1112px) and (-webkit-device-pixel-ratio: 2) and (orientation: landscape)">
  <link rel="apple-touch-startup-image" href="/static/img/splash/apple-splash-1620-2160.png"
    media="(device-width: 810px) and (device-height: 1080px) and (-webkit-device-pixel-ratio: 2) and (orientation: portrait)">
  <link rel="apple-touch-startup-image" href="/static/img/splash/apple-splash-2160-1620.png"
    media="(device-width: 810px) and (device-height: 1080px) and (-webkit-device-pixel-ratio: 2) and (orientation: landscape)">
  <link rel="apple-touch-startup-image" href="/static/img/splash/apple-splash-1284-2778.png"
    media="(device-width: 428px) and (device-height: 926px) and (-webkit-device-pixel-ratio: 3) and (orientation: portrait)">
  <link rel="apple-touch-startup-image" href="/static/img/splash/apple-splash-2778-1284.png"
    media="(device-width: 428px) and (device-height: 926px) and (-webkit-device-pixel-ratio: 3) and (orientation: landscape)">
  <link rel="apple-touch-startup-image" href="/static/img/splash/apple-splash-1170-2532.png"
    media="(device-width: 390px) and (device-height: 844px) and (-webkit-device-pixel-ratio: 3) and (orientation: portrait)">
  <link rel="apple-touch-startup-image" href="/static/img/splash/apple-splash-2532-1170.png"
    media="(device-width: 390px) and (device-height: 844px) and (-webkit-device-pixel-ratio: 3) and (orientation: landscape)">
  <link rel="apple-touch-startup-image" href="/static/img/splash/apple-splash-1125-2436.png"
    media="(device-width: 375px) and (device-height: 812px) and (-webkit-device-pixel-ratio: 3) and (orientation: portrait)">
  <link rel="apple-touch-startup-image" href="/static/img/splash/apple-splash-2436-1125.png"
    media="(device-width: 375px) and (device-height: 812px) and (-webkit-device-pixel-ratio: 3) and (orientation: landscape)">
  <link rel="apple-touch-startup-image" href="/static/img/splash/apple-splash-1242-2688.png"
    media="(device-width: 414px) and (device-height: 896px) and (-webkit-device-pixel-ratio: 3) and (orientation: portrait)">
  <link rel="apple-touch-startup-image" href="/static/img/splash/apple-splash-2688-1242.png"
    media="(device-width: 414px) and (device-height: 896px) and (-webkit-device-pixel-ratio: 3) and (orientation: landscape)">
  <link rel="apple-touch-startup-image" href="/static/img/splash/apple-splash-828-1792.png"
    media="(device-width: 414px) and (device-height: 896px) and (-webkit-device-pixel-ratio: 2) and (orientation: portrait)">
  <link rel="apple-touch-startup-image" href="/static/img/splash/apple-splash-1792-828.png"
    media="(device-width: 414px) and (device-height: 896px) and (-webkit-device-pixel-ratio: 2) and (orientation: landscape)">
  <link rel="apple-touch-startup-image" href="/static/img/splash/apple-splash-1242-2208.png"
    media="(device-width: 414px) and (device-height: 736px) and (-webkit-device-pixel-ratio: 3) and (orientation: portrait)">
  <link rel="apple-touch-startup-image" href="/static/img/splash/apple-splash-2208-1242.png"
    media="(device-width: 414px) and (device-height: 736px) and (-webkit-device-pixel-ratio: 3) and (orientation: landscape)">
  <link rel="apple-touch-startup-image" href="/static/img/splash/apple-splash-750-1334.png"
    media="(device-width: 375px) and (device-height: 667px) and (-webkit-device-pixel-ratio: 2) and (orientation: portrait)">
  <link rel="apple-touch-startup-image" href="/static/img/splash/apple-splash-1334-750.png"
    media="(device-width: 375px) and (device-height: 667px) and (-webkit-device-pixel-ratio: 2) and (orientation: landscape)">
  <link rel="apple-touch-startup-image" href="/static/img/splash/apple-splash-640-1136.png"
    media="(device-width: 320px) and (device-height: 568px) and (-webkit-device-pixel-ratio: 2) and (orientation: portrait)">
  <link rel="apple-touch-startup-image" href="/static/img/splash/apple-splash-1136-640.png"
    media="(device-width: 320px) and (device-height: 568px) and (-webkit-device-pixel-ratio: 2) and (orientation: landscape)">

  <link rel="shortcut icon" href="/static/favicon.ico" type="image/x-icon">
  <link rel="manifest" href="/static/site.webmanifest" crossorigin="use-credentials">

  <title>NAStool</title>

  <!-- CSS files -->
  <link rel="stylesheet" href="/static/libs/bootstrap/css/bootstrap.min.css" />
  <link rel="stylesheet" href="/static/libs/tabler/css/tabler.min.css" />
  <link rel="stylesheet" href="/static/libs/tabler/icon/3.34.1/tabler-icons.min.css" />
  <link rel="stylesheet" href="/static/libs/nprogress.min.css" />

  <link rel="stylesheet" href="/static/css/fullcalendar.min.css" />
  <link rel="stylesheet" href="/static/css/jquery.filetree.css" />
  <link rel="stylesheet" href="/static/css/dropzone.css" />

  <!-- 附加样式 -->
  <link rel="stylesheet" href="/static/css/style.css" />

</head>

<body class="layout-fluid">
  <div class="page">
    <!-- 解决密码填充问题 -->
    <span hidden><input type="text" aria-label=""><input type="password" aria-label=""></span>
    <layout-navbar hidden id="navbar-menu"></layout-navbar>
    <div class="spinner-border" role="status" id="logo_animation"></div>
    <div class="page-wrapper">
      <div class="input-group top-sub-navbar" id="top-sub-navbar">
        <ul class="nav nav-tabs card-header-tabs" data-bs-toggle="tabs" role="tablist"></ul>
      </div>
      <div id="loading_tips" style="display: none;">
        <loading-indicator></loading-indicator>
      </div>
      <div id="page_content" hidden="">
      </div>
    </div>
  </div>
  <!-- 加载动画遮罩 -->
  <div class="wave-loading" id="loadingOverlay">
    <div class="wave-loading-item"></div>
    <div class="wave-loading-item"></div>
    <div class="wave-loading-item"></div>
    <div class="wave-loading-item"></div>
    <div class="wave-loading-item"></div>
    <div class="wave-loading-item"></div>
    <div class="wave-loading-item"></div>
    <div class="wave-loading-item"></div>
  </div>
  <button id="scrollToTop" class="scroll-to-top" aria-label="Back to top">
    <i class="ti ti-arrow-big-up-lines"></i>
  </button>
  <div class="offcanvas offcanvas-end" tabindex="-1" id="offcanvasEnd" aria-labelledby="消息中心">
    <div class="offcanvas-header">
      <h2 class="offcanvas-title" id="offcanvasEndLabel">消息中心</h2>
      <button type="button" class="btn-close text-reset" data-bs-dismiss="offcanvas" aria-label="Close"></button>
    </div>
    <div class="offcanvas-body p-1"
      style="margin-bottom: calc(50px + env(safe-area-inset-top) + env(safe-area-inset-bottom))">
      <div class="list-group list-group-flush" id="system-messages">
      </div>
      <div class="mt-3 w-100 position-absolute mb-2 pe-2" align="center"
        style="bottom: calc(env(safe-area-inset-bottom))">
        <div class="input-group">
          <button type="button" class="btn" data-bs-toggle="dropdown" aria-haspopup="true" aria-expanded="false">
            /
          </button>
          <div class="dropdown-menu" data-popper-placement="bottom-start">
            {% for Command in Commands %}
            <a class="dropdown-item" href="javascript:send_web_message('{{ Command.id }}')">
              {{ Command.name }}
            </a>
            {% endfor %}
          </div>
          <input type="text" id="message_center_input" class="form-control" autocomplete="off" placeholder="输入消息或命令..."
            onkeydown="if(event.keyCode==13) send_web_message($(this))">
          <button class="btn btn-primary" type="button"
            onclick="send_web_message($('#message_center_input'))">发送</button>
        </div>
      </div>
    </div>
  </div>
  <!-- 等待框 -->
  <div class="modal" id="modal-wait" tabindex="-1" role="dialog" aria-hidden="true">
    <div class="modal-dialog modal-full-width modal-dialog-centered" role="document">
      <div class="spinner-border text-primary m-auto" style="width:40px;height:40px;"></div>
    </div>
  </div>
  <!-- 进度框 -->
  <div class="modal modal-blur" id="modal-process" tabindex="-1" role="dialog" aria-hidden="true">
    <div class="modal-dialog modal-md modal-dialog-centered" role="document">
      <div class="modal-content">
        <div class="card border-0">
          <div class="progress rounded-0">
            <div class="progress-bar progress-bar-striped progress-bar-animated" id="modal_process_bar" style="width: 0"
              role="progressbar" aria-valuenow="0" aria-valuemin="0" aria-valuemax="100"></div>
          </div>
          <div class="card-body text-center">
            <h3 class="card-title strong" id="modal_process_title">
            </h3>
            <small class="text-muted" id="modal_process_text">请稍候...</small>
          </div>
        </div>
      </div>
    </div>
  </div>
  <!-- 危险确认提示框 -->
  <div class="modal modal-blur fade" id="system-confirm-modal" tabindex="-1" role="dialog" aria-hidden="true">
    <div class="modal-dialog modal-sm modal-dialog-centered" role="document">
      <div class="modal-content">
        <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
        <div class="modal-status bg-danger"></div>
        <div class="modal-body text-center py-4">
          <span class="mb-2 text-danger"><i class="ti ti-alert-triangle fs-2"></i></span>
          <h3>确认</h3>
          <div class="text-muted text-wrap" id="system_confirm_message">是否确定？</div>
        </div>
        <div class="modal-footer">
          <div class="w-100">
            <div class="row">
              <div class="col">
                <a href="#" class="btn w-100" data-bs-dismiss="modal">
                  取消
                </a>
              </div>
              <div class="col">
                <button class="btn btn-danger w-100" id="system_confirm_btn">
                  确定
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
  <!-- 成功带后续操作提示框 -->
  <div class="modal modal-blur fade" id="system-success-action-modal" tabindex="-1" role="dialog" aria-hidden="true">
    <div class="modal-dialog modal-sm modal-dialog-centered" role="document">
      <div class="modal-content">
        <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
        <div class="modal-status bg-success"></div>
        <div class="modal-body text-center py-4">
          <span class="mb-2 text-green"><i class="ti ti-circle-check fs-2"></i></span>
          <h3>成功</h3>
          <div class="text-muted text-wrap" id="system_success_action_message">操作成功！</div>
        </div>
        <div class="modal-footer">
          <div class="w-100">
            <div class="row">
              <div class="col" id="system_success_action_div">
                <button class="btn w-100" id="system_success_action_btn">
                  操作
                </button>
              </div>
              <div class="col">
                <button class="btn btn-success w-100" data-bs-dismiss="modal">
                  确定
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
  <!-- 询问框 -->
  <div class="modal modal-blur fade" id="system-ask-modal" tabindex="-1" role="dialog" aria-hidden="true">
    <div class="modal-dialog modal-sm modal-dialog-centered" role="document">
      <div class="modal-content">
        <div class="modal-body">
          <div id="system_ask_title" class="modal-title">询问</div>
          <div id="system_ask_message" class="text-wrap">是否执行？</div>
        </div>
        <div class="modal-footer">
          <button type="button" class="btn btn-link link-secondary me-auto" data-bs-dismiss="modal">取消</button>
          <button type="button" class="btn btn-danger" id="system_ask_btn">确定</button>
        </div>
      </div>
    </div>
  </div>
  <!-- 成功提示 -->
  <div class="modal modal-blur fade" id="system-success-modal" tabindex="-1" role="dialog" aria-hidden="true">
    <div class="modal-dialog modal-sm modal-dialog-centered" role="document">
      <div class="modal-content">
        <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
        <div class="modal-status bg-success"></div>
        <div class="modal-body text-center py-4">
          <span class="mb-2 text-green"><i class="ti ti-circle-check fs-2"></i></span>
          <h3>成功</h3>
          <div class="text-muted text-wrap" id="system_success_message">成功！</div>
        </div>
        <div class="modal-footer">
          <div class="w-100">
            <div class="row">
              <div class="col">
                <a href="javascript:void(0)" id="system_success_modal_btn" class="btn btn-success w-100"
                  data-bs-dismiss="modal">
                  确定
                </a>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
  <!-- 失败提示 -->
  <div class="modal modal-blur fade" id="system-fail-modal" tabindex="-1" role="dialog" aria-hidden="true">
    <div class="modal-dialog modal-sm modal-dialog-centered" role="document">
      <div class="modal-content">
        <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
        <div class="modal-status bg-danger"></div>
        <div class="modal-body text-center py-4">
          <span class="mb-2 text-danger"><i class="ti ti-alert-triangle fs-2"></i></span>
          <h3>失败</h3>
          <div class="text-muted text-wrap" id="system_fail_message">错误信息</div>
        </div>
        <div class="modal-footer">
          <div class="w-100">
            <div class="row">
              <div class="col">
                <a href="#" id="system_fail_modal_btn" class="btn btn-danger w-100" data-bs-dismiss="modal">
                  确定
                </a>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
  <!-- 警告提示 -->
  <div class="modal modal-blur fade" id="system-warning-modal" tabindex="-1" role="dialog" aria-hidden="true">
    <div class="modal-dialog modal-sm modal-dialog-centered" role="document">
      <div class="modal-content">
        <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
        <div class="modal-status bg-warning"></div>
        <div class="modal-body text-center py-4">
          <span class="mb-2 text-warning"><i class="ti ti-alert-triangle fs-2"></i></span>
          <h3>注意</h3>
          <div class="text-muted text-wrap" id="system_warning_message">警告信息</div>
        </div>
        <div class="modal-footer">
          <div class="w-100">
            <div class="row">
              <div class="col">
                <a href="#" id="system_warning_modal_btn" class="btn btn-warning w-100" data-bs-dismiss="modal">
                  确定
                </a>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
  <!-- 媒体详情 -->
  <div class="modal modal-blur fade" id="system-media-modal" tabindex="-1" role="dialog" aria-hidden="true">
    <div class="modal-dialog modal-lg modal-dialog-centered" role="document">
      <div class="modal-content">
        <div class="card">
          <div class="ribbon ribbon-top ribbon-bookmark bg-purple" id="system_media_vote"></div>
          <div class="row row-0">
            <div class="col-3 d-none d-lg-block">
              <custom-img id="system_media_poster" img-class="w-100 h-100 object-cover rounded-2"
                img-ratio="150%"></custom-img>
            </div>
            <div class="col">
              <div class="card-body">
                <h4 class="card-title mb-3">
                  <a id="system_media_name_link" href="#" target="_blank"><strong
                      id="system_media_name"></strong></a><br />
                  <span class="text-muted" id="system_release_date"></span>
                </h4>
                <div class="text-muted mb-3" id="system_media_overview">
                </div>
                <div id="system_sites_info"></div>
              </div>
            </div>
          </div>
          <div class="d-flex">
            <a href="#" class="card-btn" id="system_media_url_btn">
              详情
            </a>
            <a href="javascript:void(0)" data-bs-toggle="dropdown" class="dropdown-toggle card-btn"
              id="system_media_rss_btn">
              订阅
            </a>
            <div class="dropdown-menu" id="system_media_rss_dropdown"></div>
            <a href="javascript:void(0)" class="card-btn" id="system_media_search_btn"
              style="border-left:1px solid var(--tblr-border-color)">
              搜索
            </a>
            <a href="javascript:void(0)" class="card-btn" id="system_media_refresh_btn"
              style="border-left:1px solid var(--tblr-border-color)">
              刷新
            </a>
            <a href="javascript:void(0)" class="card-btn d-none d-md-block" data-bs-dismiss="modal">
              关闭
            </a>
          </div>
        </div>
      </div>
    </div>
  </div>
  <!-- 实时日志 -->
  <div class="modal modal-blur fade" id="modal-logging" tabindex="-1" role="dialog" aria-hidden="true"
    data-bs-backdrop="static" data-bs-keyboard="false">
    <div class="modal-dialog modal-xl modal-dialog-centered modal-dialog-scrollable" role="document">
      <div class="modal-content">
        <div class="modal-header">
          <div class="select_logger">
            <h5 class="modal-title">实时日志</h5>
            <div class="dropdown">
              <button class="btn-action" data-bs-toggle="dropdown" aria-expanded="false">
                <i class="ti ti-filter-2-cog fs-2"></i>
              </button>
              <div class="dropdown-menu dropdown-menu-end" id="dropdown-menu-logger">
                {% with Loggers = ['All', 'System', 'Rss', 'RssChecker', 'Rmt', 'Meta', 'Sync', 'Sites', 'Brush',
                'Douban', 'Spider', 'Message', 'Indexer', 'Searcher', 'Subscribe', 'Downloader', 'TorrentRemover',
                'Plugin'] %}
                {% for Logger in Loggers %}
                <a class="dropdown-item" href="javascript:void(0)" onclick="logger_select('{{ Logger }}')">{{ Logger
                  }}</a>
                {% endfor %}
                {% endwith %}
              </div>
            </div>
          </div>
          <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
        </div>
        <div id="logging_table" class="table-responsive table-modal-body">
          <table class="table table-vcenter card-table table-hover table-striped">
            <thead>
              <tr>
                <th class="w-3">时间</th>
                <th class="w-3">来源</th>
                <th>内容</th>
              </tr>
            </thead>
            <tbody id="logging_content" class="table-tbody">
              <tr>
                <td colspan="3" class="text-center">刷新中...</td>
              </tr>
            </tbody>
          </table>
        </div>
        <div class="modal-footer">
          <a href="javascript:pause_logging()" class="btn btn-link me-auto" id="logging_stop_btn">暂停</a>
          <button type="button" class="btn btn-primary" data-bs-dismiss="modal">关闭</button>
        </div>
      </div>
    </div>
  </div>
  <!-- 订阅季 -->
  <div class="modal modal-blur fade" id="system-rss-seasons" tabindex="-1" role="dialog" aria-hidden="true"
    data-bs-backdrop="static" data-bs-keyboard="false">
    <div class="modal-dialog modal-lg modal-dialog-centered" role="document">
      <div class="modal-content">
        <div class="modal-header">
          <h5 class="modal-title">选择订阅季</h5>
          <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
        </div>
        <div class="modal-body">
          <div class="row">
            <div class="col">
              <div class="mb-3">
                <div class="form-selectgroup" id="system_rss_seasons_group">
                </div>
              </div>
            </div>
          </div>
        </div>
        <div class="modal-footer">
          <a href="javascript:void(0)" id="system_rss_seasons_btn" class="btn btn-primary">确定</a>
        </div>
      </div>
    </div>
  </div>
  <div class="modal modal-blur fade" id="modal-default-rss-setting" tabindex="-1" role="dialog" aria-hidden="true"
    data-bs-backdrop="static" data-bs-keyboard="false">
    <div class="modal-dialog modal-lg modal-dialog-centered modal-dialog-scrollable" role="document">
      <div class="modal-content">
        <div class="modal-header">
          <h5 class="modal-title">订阅默认设置 <span class="form-help"
              title="订阅默认设置，只适用于探索页面、交互消息、豆瓣、Jellyseerr/Overseerr等自动订阅场景" data-bs-toggle="tooltip">?</span></h5>
          <input type="hidden" id="default_rss_setting_mtype">
          <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"
            id="default_sites_close_btn"></button>
        </div>
        <div class="modal-body">
          <div class="row">
            <div class="col-lg">
              <div class="mb-3">
                <label class="form-label">质量</label>
                <select class="form-select" id="default_rss_setting_restype">
                  <option value="" selected>全部</option>
                  {% for Restype in RestypeDict %}
                  <option value="{{ Restype }}">{{ Restype }}</option>
                  {% endfor %}
                </select>
              </div>
            </div>
            <div class="col-lg">
              <div class="mb-3">
                <label class="form-label">分辨率</label>
                <select class="form-select" id="default_rss_setting_pix">
                  <option value="" selected>全部</option>
                  {% for Pix in PixDict %}
                  <option value="{{ Pix }}">{{ Pix }}</option>
                  {% endfor %}
                </select>
              </div>
            </div>
            <div class="col-lg">
              <div class="mb-3">
                <label class="form-label">制作组/字幕组</label>
                <input type="text" id="default_rss_setting_team" class="form-control" placeholder="支持正则表达式">
              </div>
            </div>
          </div>
          <div class="row">
            <div class="col-lg">
              <div class="mb-3">
                <label class="form-label">包含 <span class="form-help" title="只有种子标题或副标题中包含对应关键字或匹配对应正则表达式的才会被选中"
                    data-bs-toggle="tooltip">?</span></label>
                <input type="text" value="" id="default_rss_setting_include" class="form-control"
                  placeholder="关键字或正则表达式">
              </div>
            </div>
            <div class="col-lg">
              <div class="mb-3">
                <label class="form-label">排除 <span class="form-help" title="种子标题或副标题中包含对应关键字或匹配对应正则表达式的将会被过滤掉"
                    data-bs-toggle="tooltip">?</span></label>
                <input type="text" value="" id="default_rss_setting_exclude" class="form-control"
                  placeholder="关键字或正则表达式">
              </div>
            </div>
          </div>
          <div class="row">
            <div class="col-lg">
              <div class="mb-3">
                <label class="form-label">过滤规则 </label>
                <select class="form-select" id="default_rss_setting_rule"></select>
              </div>
            </div>
            <div class="col-lg">
              <div class="mb-3">
                <label class="form-label">下载设置</label>
                <select class="form-select" id="default_rss_setting_download_setting"></select>
              </div>
            </div>
            <div class="col-lg">
              <div class="mb-3">
                <label class="form-label">洗版 </label>
                <select class="form-select" id="default_rss_setting_over_edition">
                  <option value="0" selected>否</option>
                  <option value="1">是</option>
                </select>
              </div>
            </div>
          </div>
          <div class="row rss_sites_container" id="default_rss_setting_rss_sites_div">
            <div class="mb-3">
              <div class="btn-list">
                <label class="form-label">订阅站点</label>
                <a href="javascript:void(0)" class="ms-auto"
                  onclick="select_btn_SelectALL(this, 'default_rss_sites')">全选</a>
              </div>
              <div class="form-selectgroup" id="default_rss_setting_rss_sites_group"></div>
            </div>
          </div>
          <div class="row rss_sites_container" id="default_rss_setting_search_sites_div">
            <div class="mb-3">
              <div class="btn-list">
                <label class="form-label">搜索站点</label>
                <a href="javascript:void(0)" class="ms-auto"
                  onclick="select_btn_SelectALL(this, 'default_search_sites')">全选</a>
              </div>
              <div class="form-selectgroup" id="default_rss_setting_search_sites_group"></div>
            </div>
          </div>
        </div>
        <div class="modal-footer">
          <button onclick="save_default_rss_setting()" class="btn btn-primary">保存</button>
        </div>
      </div>
    </div>
  </div>
  <!-- 手动订阅 -->
  <div class="modal modal-blur fade" id="modal-manual-rss" tabindex="-1" role="dialog" aria-hidden="true"
    data-bs-backdrop="static" data-bs-keyboard="false">
    <div class="modal-dialog modal-lg modal-dialog-centered modal-dialog-scrollable" role="document">
      <div class="modal-content">
        <div class="modal-header">
          <h5 class="modal-title" id="rss_modal_title">新增订阅</h5>
          <input type="hidden" id="rss_id">
          <input type="hidden" id="rss_type">
          <input type="hidden" id="rss_tmdbid">
          <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
        </div>
        <div class="modal-body">
          <div class="row">
            <div class="col-lg-6">
              <div class="mb-3">
                <label class="form-label required">标题</label>
                <input type="text" value="" id="rss_name" class="form-control" placeholder="标题">
              </div>
            </div>
            <div class="col-lg-2">
              <div class="mb-3">
                <label class="form-label required">年份</label>
                <input type="text" value="" id="rss_year" class="form-control" placeholder="年份">
              </div>
            </div>
            <div class="col-lg-4">
              <div class="mb-3">
                <label class="form-label">自定义搜索词</label>
                <input type="text" value="" id="rss_keyword" class="form-control" placeholder="留空使用TMDB数据">
              </div>
            </div>
          </div>
          <div class="row" id="rss_tv_season_div">
            <div class="col-lg-4">
              <div class="mb-3">
                <label class="form-label required">季</label>
                <select class="form-select" id="rss_season">
                  <option value="" selected>请选择</option>
                  {% for i in range(1, 51) %}
                  <option value="{{ i }}">第{{ i }}季</option>
                  {% endfor %}
                </select>
              </div>
            </div>
            <div class="col-lg-4">
              <div class="mb-3">
                <label class="form-label">总集数 <span class="form-help" title="可留空应用TMDB剧集信息"
                    data-bs-toggle="tooltip">?</span></label>
                <input type="text" value="" id="rss_total_ep" class="form-control" placeholder="总集数">
              </div>
            </div>
            <div class="col-lg-4">
              <div class="mb-3">
                <label class="form-label">开始订阅集数</label>
                <input type="text" value="" id="rss_current_ep" class="form-control" placeholder="开始订阅集数">
              </div>
            </div>
          </div>
          <div class="row">
            <div class="col-xl-4">
              <div class="mb-3">
                <label class="form-check form-switch">
                  <input class="form-check-input" type="checkbox" id="fuzzy_match" onclick="change_match_check(this)">
                  <span class="form-check-label">模糊匹配 <span class="form-help"
                      title="开启后不检查TMDB是否有媒体信息，只要种子名称、标题、年份任一匹配关键字即会下载，此时标题可以配置正则表达式实现模糊匹配"
                      data-bs-toggle="tooltip">?</span></span>
                </label>
              </div>
            </div>
            <div class="col-xl-4">
              <div class="mb-3">
                <label class="form-check form-switch">
                  <input class="form-check-input" type="checkbox" id="over_edition"
                    onclick="change_over_edition_check(this)">
                  <span class="form-check-label">洗版 <span class="form-help"
                      title="开启洗版后不会检查本地是否已存在，满足订阅条件即会下载；除非匹配了对应的过滤规则中最高优先级的那条规则否则不会删除订阅（未明确过滤规则时使用默认规则），同一优先级的资源只下载一次；多个资源下载后如命名一致，则只会保留文件体积较大的，如需都保留则需要在文件重命名规则中增加相关要素以做区分"
                      data-bs-toggle="tooltip">?</span></span>
                </label>
              </div>
            </div>
          </div>
          <div class="row">
            <div class="col-lg-3">
              <div class="mb-3">
                <label class="form-label">质量</label>
                <select class="form-select" id="rss_restype">
                  <option value="" selected>全部</option>
                  {% for Restype in RestypeDict %}
                  <option value="{{ Restype }}">{{ Restype }}</option>
                  {% endfor %}
                </select>
              </div>
            </div>
            <div class="col-lg-3">
              <div class="mb-3">
                <label class="form-label">分辨率</label>
                <select class="form-select" id="rss_pix">
                  <option value="" selected>全部</option>
                  {% for Pix in PixDict %}
                  <option value="{{ Pix }}">{{ Pix }}</option>
                  {% endfor %}
                </select>
              </div>
            </div>
            <div class="col-lg-3">
              <div class="mb-3">
                <label class="form-label">制作组/字幕组</label>
                <input type="text" value="" id="rss_team" class="form-control" placeholder="支持正则表达式">
              </div>
            </div>
            <div class="col-lg-3">
              <div class="mb-3">
                <label class="form-label">过滤规则 <span class="form-help"
                    title="质量、分辨率与过滤规则为'与'的关系，过滤规则不选择时将使用站点的过滤规则，站点也未设置过滤规则时将使用默认过滤规则"
                    data-bs-toggle="tooltip">?</span></label>
                <select class="form-select" id="rss_rule">
                </select>
              </div>
            </div>
          </div>
          <div class="row">
            <div class="col-lg">
              <div class="mb-3">
                <label class="form-label">包含 <span class="form-help" title="只有种子标题或副标题中包含对应关键字或匹配对应正则表达式的才会被选中"
                    data-bs-toggle="tooltip">?</span></label>
                <input type="text" value="" id="rss_include" class="form-control" placeholder="关键字或正则表达式">
              </div>
            </div>
            <div class="col-lg">
              <div class="mb-3">
                <label class="form-label">排除 <span class="form-help" title="种子标题或副标题中包含对应关键字或匹配对应正则表达式的将会被过滤掉"
                    data-bs-toggle="tooltip">?</span></label>
                <input type="text" value="" id="rss_exclude" class="form-control" placeholder="关键字或正则表达式">
              </div>
            </div>
          </div>
          <div class="row">
            <div class="col-lg-4">
              <div class="mb-3">
                <label class="form-label">下载设置</label>
                <select class="form-select" id="rss_download_setting"
                  onchange="refresh_savepath_select('rss_save_path', true, $(this).val())">
                </select>
              </div>
            </div>
            <div class="col-lg-8">
              <div class="mb-3">
                <label class="form-label">保存路径</label>
                <select class="form-select" id="rss_save_path" aria-label="保存目录"
                  onchange="check_manual_input_path('rss_save_path','rss_save_path_manual')">
                </select>
                <input type="text" value="" id="rss_save_path_manual" class="form-control" style="display: none"
                  placeholder="留空自动选择保存路径">
              </div>
            </div>
          </div>
          <div class="row rss_sites_container" id="rss_sites_div">
            <div class="mb-3">
              <div class="btn-list">
                <label class="form-label">订阅站点</label>
                <a href="javascript:void(0)" class="ms-auto" onclick="select_btn_SelectALL(this, 'rss_sites')">全选</a>
              </div>
              <div class="form-selectgroup" id="rss_sites_group"></div>
            </div>
          </div>
          <div class="row rss_sites_container" id="rss_search_sites_div">
            <div class="mb-3">
              <div class="btn-list">
                <label class="form-label">搜索站点</label>
                <a href="javascript:void(0)" class="ms-auto" onclick="select_btn_SelectALL(this, 'search_sites')">全选</a>
              </div>
              <div class="form-selectgroup" id="rss_search_sites_group"></div>
            </div>
          </div>
        </div>
        <div class="modal-footer">
          <a href="#" class="btn btn-link text-red me-auto" id="rss_delete_btn">
            取消订阅
          </a>
          <div class="btn-list">
            <a href="javascript:add_rss_manual(true)" name="rss_add_btn" class="btn btn-secondary">添加并继续</a>
            <a href="javascript:add_rss_manual(false)" name="rss_add_btn" class="btn btn-primary">添加</a>
            <a href="javascript:add_rss_manual(false)" name="rss_edit_btn" class="btn btn-primary">确定</a>
          </div>
        </div>
      </div>
    </div>
  </div>
  <!-- 手动识别 -->
  <div class="modal modal-blur fade" id="modal-media-identification" tabindex="-1" role="dialog" aria-hidden="true"
    data-bs-backdrop="static" data-bs-keyboard="false">
    <div class="modal-dialog modal-lg modal-dialog-centered modal-dialog-scrollable" role="document">
      <div class="modal-content">
        <div class="modal-header">
          <h5 class="modal-title" id="rename_header">手动识别</h5>
          <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
        </div>
        <div class="modal-body">
          <div class="row" id="rename_path_div">
            <div class="col-lg-9">
              <div class="mb-3">
                <label class="form-label required">路径</label>
                <input type="text" id="rename_path" class="form-control" readonly>
                <input type="hidden" id="rename_source">
                <input type="hidden" id="rename_manual_type">
                <input type="hidden" id="unknown_id">
                <input type="hidden" id="transferlog_id" class="form-control">
              </div>
            </div>
            <div class="col-lg-3">
              <div class="mb-3">
                <label class="form-label required">转移方式</label>
                <select id="rename_syncmod_manual" class="form-select">
                  {% for mode in RmtModeDict %}
                  <option value="{{ mode.value }}" {% if SyncMod==mode.value %}selected{% endif %}>{{ mode.name }}
                  </option>
                  {% endfor %}
                </select>
              </div>
            </div>
          </div>
          <div class="mb-3" id="rename_inpath_div">
            <label class="form-label required">输入路径</label>
            <input type="text" id="rename_inpath" class="form-control filetree-folders-only" placeholder="需要转移的目录或文件"
              autocomplete="off">
          </div>
          <div class="row" id="rename_outpath_div">
            <div class="col-lg-9">
              <div class="mb-3">
                <label class="form-label">输出路径</label>
                <input type="text" id="rename_outpath" class="form-control filetree-folders-only"
                  placeholder="转移后文件存储路径，留空则转移至媒体库" autocomplete="off">
              </div>
            </div>
            <div class="col-lg-3">
              <div class="mb-3">
                <label class="form-label required">转移方式</label>
                <select id="rename_syncmod_customize" class="form-select">
                  {% for mode in RmtModeDict %}
                  <option value="{{ mode.value }}" {% if SyncMod==mode.value %}selected{% endif %}>{{ mode.name }}
                  </option>
                  {% endfor %}
                </select>
              </div>
            </div>
          </div>
          <label class="form-label">类型</label>
          <div class="form-selectgroup-boxes row mb-3">
            <div class="col-lg-4">
              <div class="mb-1">
                <label class="form-selectgroup-item">
                  <input type="radio" id="rename_type_mov" name="rename_type" value="MOV" class="form-selectgroup-input"
                    onchange="switch_rename_type(this)" checked>
                  <span class="form-selectgroup-label d-flex align-items-center p-3">
                    <span class="me-3">
                      <span class="form-selectgroup-check"></span>
                    </span>
                    <span class="form-selectgroup-label-content">
                      <span class="form-selectgroup-title strong mb-1">电影</span>
                    </span>
                  </span>
                </label>
              </div>
            </div>
            <div class="col-lg-4">
              <div class="mb-1">
                <label class="form-selectgroup-item">
                  <input type="radio" id="rename_type_tv" name="rename_type" value="TV"
                    onchange="switch_rename_type(this)" class="form-selectgroup-input">
                  <span class="form-selectgroup-label d-flex align-items-center p-3">
                    <span class="me-3">
                      <span class="form-selectgroup-check"></span>
                    </span>
                    <span class="form-selectgroup-label-content">
                      <span class="form-selectgroup-title strong mb-1">电视剧</span>
                    </span>
                  </span>
                </label>
              </div>
            </div>
            <div class="col-lg-4">
              <div class="mb-1">
                <label class="form-selectgroup-item">
                  <input type="radio" id="rename_type_anime" name="rename_type" value="ANIME"
                    onchange="switch_rename_type(this)" class="form-selectgroup-input">
                  <span class="form-selectgroup-label d-flex align-items-center p-3">
                    <span class="me-3">
                      <span class="form-selectgroup-check"></span>
                    </span>
                    <span class="form-selectgroup-label-content">
                      <span class="form-selectgroup-title strong mb-1">动漫</span>
                    </span>
                  </span>
                </label>
              </div>
            </div>
          </div>
          <div class="row">
            <div class="col-lg-4">
              <div class="mb-3">
                <label class="form-label">TMDB ID</label>
                <div class="input-group">
                  <input type="text" id="rename_tmdb" class="form-control" placeholder="留空自动识别">
                  <button class="btn" type="button"
                    onclick="show_search_tmdbid_modal('rename_tmdb', 'modal-media-identification')">查询</button>
                </div>
              </div>
            </div>
            <div class="col-lg-4" id="rename_season_div">
              <div class="mb-3">
                <label class="form-label">季</label>
                <select class="form-select" id="rename_season">
                  <option value="" selected>请选择</option>
                  {% for i in range(0, 51) %}
                  <option value="{{ i }}">第{{ i }}季</option>
                  {% endfor %}
                </select>
              </div>
            </div>
            <div class="col-lg-4" id="rename_min_filesize_div">
              <div class="mb-3">
                <label class="form-label">最小文件大小 <span class="form-help" title="留空将使用基础设置中的转移最小文件大小设置，如不限制大小需输入0"
                    data-bs-toggle="tooltip">?</span></label>
                <input type="text" id="rename_min_filesize" class="form-control" placeholder="最小文件大小(MB)">
              </div>
            </div>
            <div class="col-lg-12" id="rename_specify_episode_div">
              <div class="mb-3">
                <label class="form-label">集数指定 <span class="form-help"
                    title="2个都不填，用默认识别 <br>1、指定具体集数 <br>&nbsp;&nbsp;&nbsp;&nbsp;例如 '1' 表示第一集, '1-2'表示第1-2集(1,2合起来当作一集)，此选项优先级最高。<br>2、part: 需符合以下正则写法(PART[0-9ABCI]{0,2}|^CD[0-9]{0,2}|^DVD[0-9]{0,2}|^DISK[0-9]{0,2}|^DISC[0-9]{0,2})：例如Part1, PartC, PartIII, Cd1, Dvd2, Disk10, Disc12"
                    data-bs-toggle="tooltip" data-bs-html="true">?</span></label>
                <div class="row">
                  <div class="col-4">
                    <input type="text" id="rename_specify_episode" class="form-control" placeholder="指定集数">
                  </div>
                  <div class="col-4">
                    <input type="text" id="rename_specify_part" class="form-control" placeholder="指定Part">
                  </div>
                </div>
              </div>
            </div>
            <div class="col-lg-12" id="rename_episode_div">
              <div class="mb-3">
                <label class="form-label">高级集数定位 <span class="form-help"
                    title="3个都不填，用默认识别<br>1、{ep}: 标定集数位置，例如：<br>&nbsp;&nbsp;&nbsp;&nbsp;'(BD)十二国記 第45話「東の海神 西の滄海 五章」(1440x1080 x264-10bpp flac).mkv'<br>&nbsp;&nbsp;&nbsp;&nbsp;'(BD)十二国記 第32話「風の万里 黎明の空　九章」(1440x1080 x264-10bpp flac).mkv'<br>&nbsp;&nbsp;&nbsp;&nbsp;此处可以填'(BD)十二国記 第{ep}話{a}(1440x1080 x264-10bpp flac).mkv' ep表示集，a表示理解成一个变量，随意用一个变量表示，如果没有变化的部分，只标定ep就行。<br>2、起始集,终止集：裁定处理集数过滤范围，<br>&nbsp;&nbsp;&nbsp;&nbsp;例如'2,4' 只取第2集到第4集。<br>3、集数偏移：例如ep定位出集数是11, 实际是第1集, 此处填-10, 以应付多季合集的场景。"
                    data-bs-toggle="tooltip" data-bs-html="true">?</span></label>
                <div class="row">
                  <div class="col-4">
                    <input type="text" id="rename_episode" class="form-control" placeholder="{ep}定位集数">
                  </div>
                  <div class="col-4">
                    <input type="text" id="rename_episode_details" class="form-control" placeholder="起始集,终止集，如1,2">
                  </div>
                  <div class="col-4">
                    <input type="text" id="rename_episode_offset" class="form-control" placeholder="集数偏移，如-10">
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
        <div class="modal-footer">
          <button type="button" class="btn btn-link me-auto" data-bs-dismiss="modal">取消</button>
          <button type="button" id="identification_action_btn" class="btn btn-primary"
            onclick="manual_media_transfer()">转移
          </button>
        </div>
      </div>
    </div>
  </div>
  <!-- 手动下载 -->
  <div class="modal modal-blur fade" id="modal-search-download" tabindex="-1" role="dialog" aria-hidden="true">
    <div class="modal-dialog modal-md modal-dialog-centered" role="document">
      <input type="hidden" id="search_download_id">
      <input type="hidden" id="search_download_name">
      <div class="modal-content">
        <div class="modal-body">
          <div class="modal-title">添加下载</div>
          <div class="row mt-3">
            <div class="col-md">
              <div class="form-floating mb-1">
                <select class="form-select" id="search_download_setting" aria-label="下载设置"
                  onchange="refresh_savepath_select('search_download_dir', true, $(this).val())">
                </select>
                <label for="search_download_setting">下载设置</label>
              </div>
            </div>
            <div class="col-md">
              <div class="form-floating mb-1">
                <select class="form-select" id="search_download_dir" aria-label="保存目录"
                  onchange="check_manual_input_path('search_download_dir', 'search_download_dir_manual')"></select>
                <input type="text" value="" id="search_download_dir_manual" class="form-control" style="display: none"
                  placeholder="留空自动选择保存路径">
                <label for="search_download_dir">保存目录</label>
              </div>
            </div>
          </div>
          <form class="dropzone mt-2" id="torrent_files" action="/upload">
            <div class="fallback">
              <input name="file" type="file" accept="*.zip" />
            </div>
            <div class="dz-message">
              <h3 class="dropzone-msg-title">上传种子文件</h3>
              <span class="dropzone-msg-desc">点击或者拖动种子文件到此处</span>
            </div>
          </form>
          <textarea class="form-control mt-2" id="torrent_urls" rows="8" placeholder="https://xxxx，换行添加多个链接"
            aria-label=""></textarea>
        </div>
        <div class="modal-footer">
          <button type="button" class="btn btn-link link-secondary me-auto" data-bs-dismiss="modal">取消</button>
          <button type="button" class="btn btn-primary" id="search_download_btn" title="立即开始下载">
            下载
          </button>
        </div>
      </div>
    </div>
  </div>
  <!-- 根据名称查找TMDBID的对话框 -->
  <div class="modal modal-blur fade" id="search-tmdbid-modal" tabindex="-1" role="dialog" aria-hidden="true"
    data-bs-backdrop="static" data-bs-keyboard="false">
    <div class="modal-dialog modal-lg modal-dialog-centered modal-dialog-scrollable" role="document">
      <div class="modal-content">
        <div class="modal-header">
          <h5 class="modal-title">查询TMDBID</h5>
          <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
        </div>
        <div class="modal-body">
          <div class="row">
            <div class="col">
              <input id="search_tmdbid_name" type="text" class="form-control" placeholder="输入名称查询"
                onkeydown="if(event.keyCode==13) search_tmdbid_by_name('search_tmdbid_name', 'search_tmdbid_list')">
            </div>
            <div class="col-auto">
              <button onclick="search_tmdbid_by_name('search_tmdbid_name', 'search_tmdbid_list')"
                class="btn btn-primary btn-icon" aria-label="Button">
                <i class="ti ti-search"></i>
              </button>
            </div>
          </div>
          <div class="row mt-3">
            <div class="list-group list-group-flush" id="search_tmdbid_list">
            </div>
          </div>
        </div>
        <div class="modal-footer">
          <button id="search_tmdbid_btn" class="btn btn-primary">确定</button>
        </div>
      </div>
    </div>
  </div>
  <div class="modal modal-blur fade" id="modal-search-advanced" tabindex="-1" role="dialog" aria-hidden="true"
    data-bs-backdrop="static" data-bs-keyboard="false">
    <div class="modal-dialog modal-lg modal-dialog-centered modal-dialog-scrollable" role="document">
      <div class="modal-content">
        <div class="modal-header">
          <h5 class="modal-title">高级搜索</h5>
          <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
        </div>
        <div class="modal-body">
          <div class="row">
            <div class="col-12 col-lg-4">
              <div class="mb-3">
                <label class="form-label">类型</label>
                <select class="form-select" id="advanced_search_type">
                  <option value="" selected>全部</option>
                  <option value="电影">电影</option>
                  <option value="电视剧">电视剧</option>
                </select>
              </div>
            </div>
            <div class="col-12 col-lg-4">
              <div class="mb-3">
                <label class="form-label required">名称</label>
                <input type="text" value="" id="advanced_search_name" class="form-control" placeholder="电影/电视剧名称">
              </div>
            </div>
            <div class="col-12 col-lg-2">
              <div class="mb-3">
                <label class="form-label">年份</label>
                <input type="text" value="" id="advanced_search_year" class="form-control" placeholder="20xx">
              </div>
            </div>
            <div class="col-12 col-lg-2">
              <div class="mb-3">
                <label class="form-label">季</label>
                <select class="form-select" id="advanced_search_season">
                  <option value="" selected>全部</option>
                  {% for i in range(1, 51) %}
                  <option value="{{ i }}">第{{ i }}季</option>
                  {% endfor %}
                </select>
              </div>
            </div>
          </div>
          <div class="row">
            <div class="col-12 col-lg-4">
              <div class="mb-3">
                <label class="form-label">站点</label>
                <select class="form-select" id="advanced_search_site">
                  <option value="" selected>全部</option>
                  {% for site in Indexers %}
                  <option value="{{ site.name }}">{{ site.name }}</option>
                  {% endfor %}
                </select>
              </div>
            </div>
            <div class="col-12 col-lg-4">
              <div class="mb-3">
                <label class="form-label">质量</label>
                <select class="form-select" id="advanced_search_restype">
                  <option value="" selected>全部</option>
                  {% for Restype in RestypeDict %}
                  <option value="{{ Restype }}">{{ Restype }}</option>
                  {% endfor %}
                </select>
              </div>
            </div>
            <div class="col-12 col-lg-4">
              <div class="mb-3">
                <label class="form-label">分辨率</label>
                <select class="form-select" id="advanced_search_pix">
                  <option value="" selected>全部</option>
                  {% for Pix in PixDict %}
                  <option value="{{ Pix }}">{{ Pix }}</option>
                  {% endfor %}
                </select>
              </div>
            </div>
            <div class="col-12 col-lg-4">
              <div class="mb-3">
                <label class="form-label">促销</label>
                <select class="form-select" id="advanced_sp_state">
                  <option value="* *" selected>全部</option>
                  <option value="1.0 1.0">普通</option>
                  <option value="1.0 0.0">免费</option>
                  <option value="2.0 1.0">2X</option>
                  <option value="2.0 0.0">2X免费</option>
                  <option value="1.0 0.5">50%</option>
                  <option value="2.0 0.5">2X 50%</option>
                  <option value="1.0 0.7">70%</option>
                  <option value="1.0 0.3">30%</option>
                </select>
              </div>
            </div>
            <div class="col-12 col-lg-8">
              <div class="mb-3">
                <label class="form-label">过滤规则</label>
                <select id="advanced_search_rule" class="form-control"></select>
              </div>
            </div>
          </div>
        </div>
        <div class="modal-footer">
          <button onclick="search_media_advanced()" id="search_advanced_btn" class="btn btn-primary">开始搜索
          </button>
        </div>
      </div>
    </div>
  </div>

  <!-- 基础库 -->
  <script src="/static/js/jquery-3.3.1.min.js"></script>
  <script src="/static/libs/bootstrap/js/bootstrap.bundle.min.js"></script>
  <script src="/static/libs/tabler/js/tabler.min.js"></script>
  <script src="/static/libs/navigo.min.js"></script>
  <script src="/static/libs/nprogress.min.js"></script>
  <!-- axios -->
  <script src="/static/js/axios.min.js"></script>
  <!-- 认证管理和axios封装 -->
  <script src="/static/js/auth-manager.js"></script>
  <script src="/static/js/axios-wrapper.js"></script>
  <!-- 载入业务脚本 -->
  <script src="/static/js/app.js"></script>
  <script src="/static/js/util.js"></script>
  <script src="/static/js/functions.js"></script>
  <!-- 引入组件定义 -->
  <script src="/static/js/oops.js"></script>
  <script src="/static/components/index.js"></script>

  <!-- 第三方组件 -->
  <script src="/static/js/ace/ace.js"></script>
  <script src="/static/js/modules/echarts.min.js"></script>
  <script src="/static/js/modules/dom-to-image.min.js"></script>
  <script src="/static/js/modules/FileSaver.min.js"></script>
  <script src="/static/js/modules/numeral.min.js"></script>
  <script src="/static/js/modules/moment.min.js"></script>
  <script src="/static/js/modules/jquery.filetree.js"></script>
  <script src="/static/js/modules/reconnecting-websocket.js"></script>
  <script src="/static/js/fullcalendar/fullcalendar.min.js"></script>
  <script src="/static/js/fullcalendar/locales/zh-cn.js"></script>
  <!-- lit -->
  <script type="module" src="/static/components/lit-index.js"></script>

  <!-- 首页事件  -->
  <script type="text/javascript">

    // 页面初始事件
    $(document).ready(function () {

      const scrollToTopBtn = document.getElementById('scrollToTop');
      let topBtnShowTime = null;

      // 检查滚动条位置
      function checkScrollPosition() {

        const canScroll = document.documentElement.scrollHeight > document.documentElement.clientHeight;

        if (canScroll) {
          scrollToTopBtn.classList.add('show');
        } else {
          scrollToTopBtn.classList.remove('show');
          // 清除定时器避免内存泄漏
          clearTimeout(topBtnShowTime);
          topBtnShowTime = null;
        }
      }

      function resetHideTimeout() {
        // 只有当按钮可见时才重置定时器
        if (scrollToTopBtn.classList.contains('show')) {
          clearTimeout(topBtnShowTime);
          topBtnShowTime = setTimeout(() => {
            scrollToTopBtn.classList.remove('show');
          }, 3000);
        }
      }

      // 初始化检查
      checkScrollPosition();

      // 监听滚动事件
      window.addEventListener('scroll', () => {
        checkScrollPosition();
        resetHideTimeout();
      });

      // 监听窗口大小变化
      window.addEventListener('resize', () => {
        checkScrollPosition();
        resetHideTimeout();
      });

      // 滚动到顶部功能
      scrollToTopBtn.addEventListener('click', () => {
        window.scrollTo({
          top: 0,
          behavior: 'smooth'
        });
      });

      // 初始化和窗口大小变化时更新
      document.addEventListener("DOMContentLoaded", updateTabDisplay);

      window.addEventListener("resize", updateTabDisplay);

      // 监听页面状态
      window.addEventListener('popstate', function (e) {
        if (e.state) {
          // 开始还原页面
          if (e.state.CurrentPage) {
            sessionStorage.CurrentPage = e.state.CurrentPage;
          }
          CurrentPageUri = e.state.page;
          document.querySelector("#navbar-menu").update_active();
          $(window).unbind('scroll');
          document.title = e.state.title;
          $('#page_content').html(e.state.html).ready(function () {
            $(window).scrollTop(e.state.scroll);
          })
        }
      });

      // 浏览器关闭事件
      window.addEventListener("beforeunload", function () {
        // 关闭连接
        stop_message();
        stop_logging();
        stop_progress();
      });

      // tooltip点击事件处理，阻止冒泡到上级元素，避免比如`点击tooltip切换了switch`的问题
      $('body').on('click', 'span[data-bs-toggle="tooltip"]', function (e) {
        e.preventDefault();
        e.stopPropagation();
      });

      // WebSocket刷新消息中心
      connect_message();

      // 初始化系统信息和组件
      initializeSystemInfo();

    });

    // 初始化系统信息函数
    function initializeSystemInfo() {
      // 调用 /data/sysinfo 接口获取系统信息
      axios_post("/data/sysinfo", {}, function (response) {

        if (response.code === 0 && response.data) {

          const sysInfo = response.data;

          // 动态生成搜索栏 HTML
          var searchbar = createSearchbarHTML(sysInfo);

          // 在 #navbar-menu 后插入
          $("#navbar-menu").after(searchbar);

          // 延迟初始化搜索栏功能，确保 DOM 已经渲染
          setTimeout(function() {
            initializeSearchbar(sysInfo);
          }, 50);

          // 全局变量默认值
          DefaultTransferMode = sysInfo.SyncMod;
          DefaultPath = sysInfo.DefaultPath;
          // 版本号
          $('#version-txt').text(sysInfo.AppVersion);
          // 动态设置 body 背景图
          // $("body").css("background-image", `url(data:image/jpg;base64,${sysInfo.imgLink})`);

          // 检查tmdb配置
          if (!sysInfo.TMDBFlag) {
            show_init_alert_modal();
          }

        } else {
          console.error('获取系统信息失败:', response);
        }
      }, function (error) {
        console.error('调用系统信息接口失败:', error);
      });
    }

    // 创建搜索栏 HTML
    function createSearchbarHTML(sysInfo) {
      return `
        <div class="navbar fixed-top lit-searchbar" id="dynamic-searchbar" style="z-index: 1040;">
          <div class="container-fluid nav-search-bar">
            <div class="d-flex flex-row flex-grow-1 align-items-center py-1">
              <!-- 导航展开按钮 -->
              <button class="navbar-toggler d-lg-none ms-2" type="button" data-bs-toggle="offcanvas" data-bs-target="#litLayoutNavbar">
                <span class="navbar-toggler-icon"></span>
              </button>
              <!-- 搜索栏 -->
              <div class="input-group input-group-flat mx-2">
                <span class="input-group-text form-control-rounded">
                  <a href="#" class="link-secondary" id="search-source-toggle">
                    <svg xmlns="http://www.w3.org/2000/svg" class="icon icon-tabler icon-tabler-square-letter-t text-blue" width="24" height="24"
                       viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" fill="none" stroke-linecap="round"
                       stroke-linejoin="round" id="search-source-icon">
                      <path stroke="none" d="M0 0h24v24H0z" fill="none"></path>
                      <path d="M4 4m0 2a2 2 0 0 1 2 -2h12a2 2 0 0 1 2 2v12a2 2 0 0 1 -2 2h-12a2 2 0 0 1 -2 -2z"></path>
                      <path d="M10 8h4"></path>
                      <path d="M12 8v8"></path>
                    </svg>
                  </a>
                </span>
                <input type="text" class="home_search_bar form-control form-control-rounded" placeholder="搜索电影、电视剧" autocomplete="new-password" id="search-input">
                <span class="input-group-text form-control-rounded">
                  <a href="${sysInfo.search > 0 ? "javascript:show_search_advanced_modal()":"javascript:void(0)"}" class="link-secondary">
                    <i class="ti ti-adjustments fs-2"></i>
                  </a>
                </span>
              </div>
              <!-- 头像 -->
              <div class="nav-item dropdown me-2">
                  <a href="#" class="nav-link d-flex lh-1 text-reset ms-1 p-0" data-bs-toggle="dropdown" aria-expanded="false">
                    <i class="ti ti-user fs-2"></i>
                  </a>
                  <div class="dropdown-menu dropdown-menu-end dropdown-menu-arrow" style="z-index: 9999; position: absolute;">
                    <a class="dropdown-item hide-theme-dark" href="?theme=dark" role="button">暗黑风格</a>
                    <a class="dropdown-item hide-theme-light" href="?theme=light" role="button">明亮风格</a>
                    <div class="dropdown-divider"></div>
                    ${sysInfo.admin === 1 ? `
                      <a class="dropdown-item" data-bs-toggle="offcanvas" href="#offcanvasEnd" role="button"
                        aria-controls="offcanvasEnd">消息中心</a>
                      <a class="dropdown-item" href="javascript:show_logging_modal()" role="button">实时日志</a>
                      <div class="dropdown-divider"></div>
                      ${["Docker", "Synology"].includes(sysInfo.SystemFlag) ?
                        '<a href="javascript:restart()" class="dropdown-item">重启</a>' : ''}
                    ` : ''}
                    <a href="javascript:logout()" class="dropdown-item">
                      注销 <span class="text-muted mx-3">${sysInfo.username}</span>
                    </a>
                    <div class="dropdown-divider"></div>
                  </div>
                </div>
              </div>
          </div>
        </div>
      `;
    }

    // 初始化搜索栏功能
    function initializeSearchbar(sysInfo) {
      let searchSource = localStorage.getItem("SearchSource") || "tmdb";
      let blur = false;

      // 搜索源图标定义
      const searchSourceIcons = {
        tmdb: `<svg xmlns="http://www.w3.org/2000/svg" class="icon icon-tabler icon-tabler-square-letter-t text-blue" width="24" height="24"
                 viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" fill="none" stroke-linecap="round"
                 stroke-linejoin="round">
                <path stroke="none" d="M0 0h24v24H0z" fill="none"></path>
                <path d="M4 4m0 2a2 2 0 0 1 2 -2h12a2 2 0 0 1 2 2v12a2 2 0 0 1 -2 2h-12a2 2 0 0 1 -2 -2z"></path>
                <path d="M10 8h4"></path>
                <path d="M12 8v8"></path>
              </svg>`,
        douban: `<svg xmlns="http://www.w3.org/2000/svg" class="icon icon-tabler icon-tabler-circle-letter-d text-green" width="24" height="24"
                  viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" fill="none" stroke-linecap="round"
                  stroke-linejoin="round">
                <path stroke="none" d="M0 0h24v24H0z" fill="none"></path>
                <path d="M12 12m-9 0a9 9 0 1 0 18 0a9 9 0 1 0 -18 0"></path>
                <path d="M10 8v8h2a2 2 0 0 0 2 -2v-4a2 2 0 0 0 -2 -2h-2z"></path>
              </svg>`,
        person: `<svg xmlns="http://www.w3.org/2000/svg" class="icon icon-tabler icon-tabler-square-rounded-letter-p text-purple" width="24" height="24" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" fill="none" stroke-linecap="round" stroke-linejoin="round">
                 <path stroke="none" d="M0 0h24v24H0z" fill="none"></path>
                 <path d="M10 12h2a2 2 0 1 0 0 -4h-2v8"></path>
                 <path d="M12 3c7.2 0 9 1.8 9 9s-1.8 9 -9 9s-9 -1.8 -9 -9s1.8 -9 9 -9z"></path>
              </svg>`
      };

      // 更新搜索源图标和占位符
      function updateSearchSource() {
        $("#search-source-icon").html(searchSourceIcons[searchSource]);
        const placeholder = searchSource === "person" ? "搜索人物" : "搜索电影、电视剧";
        $("#search-input").attr("placeholder", placeholder);
      }

      // 初始化搜索源
      updateSearchSource();

      // 搜索源切换事件
      $("#search-source-toggle").click(function(e) {
        e.preventDefault();
        const sourceDict = {
          tmdb: "douban",
          douban: "person",
          person: "tmdb"
        };
        searchSource = sourceDict[searchSource];
        localStorage.setItem("SearchSource", searchSource);
        updateSearchSource();
      });

      // 搜索输入事件
      $("#search-input").keypress(function(e) {
        if (e.which === 13 && $(this).val()) {
          const keyword = $(this).val();
          if (searchSource === "person") {
            navmenu("discovery_person?&type=ALL&title=演员搜索&subtitle=" + keyword + "&keyword=" + keyword);
          } else {
            navmenu("recommend?type=SEARCH&title=搜索结果&subtitle=" + keyword + "&keyword=" + keyword + "&source=" + searchSource);
          }
          $(this).val("");
        }
      });

      // 滚动模糊效果
      $(window).scroll(function() {
        const scrollLength = $(window).scrollTop();
        const searchbar = $("#dynamic-searchbar");

        if (!blur && scrollLength >= 5) {
          blur = true;
          searchbar.addClass("lit-searchbar-blur");
        } else if (blur && scrollLength < 5) {
          blur = false;
          searchbar.removeClass("lit-searchbar-blur");
        }
      });

      // 初始化 Bootstrap 下拉菜单 - 使用 Bootstrap 原生功能
      setTimeout(function() {
        // 尝试使用 Bootstrap 原生下拉菜单
        if (typeof bootstrap !== 'undefined' && bootstrap.Dropdown) {
          var dropdownElement = document.querySelector('#dynamic-searchbar [data-bs-toggle="dropdown"]');
          if (dropdownElement) {
            // 使用 Bootstrap 的 Dropdown 组件，它会自动处理位置
            new bootstrap.Dropdown(dropdownElement, {
              boundary: 'viewport',  // 确保下拉菜单不超出视窗
              popperConfig: {
                placement: 'bottom-end',
                modifiers: [
                  {
                    name: 'preventOverflow',
                    options: {
                      boundary: 'viewport',
                    },
                  },
                  {
                    name: 'flip',
                    options: {
                      fallbackPlacements: ['bottom-start', 'top-end', 'top-start'],
                    },
                  },
                ],
              }
            });
          }
        } else {
          // 备用方案：手动实现
          $('#dynamic-searchbar [data-bs-toggle="dropdown"]').on('click', function(e) {
            e.preventDefault();
            var $dropdown = $(this).next('.dropdown-menu');

            // 切换显示状态
            if ($dropdown.hasClass('show')) {
              $dropdown.removeClass('show');
              $(this).attr('aria-expanded', 'false');
            } else {
              // 先隐藏其他下拉菜单
              $('.dropdown-menu.show').removeClass('show');
              $('[data-bs-toggle="dropdown"][aria-expanded="true"]').attr('aria-expanded', 'false');

              // 显示当前下拉菜单
              $dropdown.addClass('show');
              $(this).attr('aria-expanded', 'true');

              // 调整下拉菜单位置，确保不超出视窗
              setTimeout(function() {
                var dropdownRect = $dropdown[0].getBoundingClientRect();
                var viewportWidth = window.innerWidth;
                var viewportHeight = window.innerHeight;

                // 如果右边超出视窗，调整到左边
                if (dropdownRect.right > viewportWidth) {
                  $dropdown.removeClass('dropdown-menu-end').addClass('dropdown-menu-start');
                }

                // 如果下边超出视窗，调整到上边
                if (dropdownRect.bottom > viewportHeight) {
                  $dropdown.css({
                    'top': 'auto',
                    'bottom': '100%',
                    'margin-bottom': '0.125rem'
                  });
                }
              }, 10);
            }
          });

          // 点击其他地方时隐藏下拉菜单
          $(document).on('click', function(e) {
            if (!$(e.target).closest('.dropdown').length) {
              $('.dropdown-menu.show').removeClass('show').removeAttr('style');
              $('[data-bs-toggle="dropdown"][aria-expanded="true"]').attr('aria-expanded', 'false');
            }
          });
        }
      }, 200);
    }

  </script>

</body>

</html>