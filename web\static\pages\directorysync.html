<style>
  .w-1 {
    text-align: center;
  }

</style>

<div class="container-xl">
  <!-- Page title -->
  <div class="page-header d-print-none">
    <div class="row align-items-center">
      <div class="col">
      </div>
      <div class="col-auto ms-auto d-print-none">
        <div class="btn-list">
          <a href="javascript:show_add_sync_path_modal()" class="btn btn-primary d-none d-sm-inline-flex">
            <i class="ti ti-plus fs-2"></i>
            新增同步目录
          </a>
          <a href="javascript:show_add_sync_path_modal()" class="btn btn-primary d-sm-none btn-icon">
            <i class="ti ti-plus fs-2"></i>
          </a>
        </div>
      </div>
    </div>
  </div>
</div>

<!-- 业务页面代码 -->
<div class="page-body">
  <div class="container-xl">
    <div class="row row-cards">
      <div class="col-12">
        <div class="card">
          <div class="table-responsive" >
            <table class="table table-vcenter card-table table-hover table-striped">
              <thead>
                <tr>
                  <th class="w-1"></th>
                  <th>源目录</th>
                  <th>目的目录</th>
                  <th>未识别目录</th>
                  <th class="w-1">同步方式</th>
                  <th class="w-1">兼容模式</th>
                  <th class="w-1">识别重命名</th>
                  <th class="w-1"></th>
                  <th class="w-1"></th>
                </tr>
              </thead>
              <tbody>
                {% for Id, Attr in SyncPaths.items() %}
                <tr>
                  <td class="w-1">
                    {% if Attr.enabled %}
                    <span class="badge bg-green">启用</span>
                    {% else %}
                    <span class="badge bg-red">停用</span>
                    {% endif %}
                  </td>
                  <td>{{ Attr.from or '' }}</td>
                  <td>{{ Attr.to or '' }}</td>
                  <td>{{ Attr.unknown or '未设置' }}</td>
                  <td class="w-1"><span class="badge">{{ Attr.syncmod_name }}</span></td>
                  <td class="w-1">
                    {% if Attr.compatibility %}
                    <span class="badge bg-cyan">开</span>
                    {% else %}
                    <span class="badge bg-orange">关</span>
                    {% endif %}
                  </td>
                  <td class="w-1">
                    {% if Attr.rename %}
                    <span class="badge bg-cyan">开</span>
                    {% else %}
                    <span class="badge bg-orange">关</span>
                    {% endif %}
                  </td>
                  <td class="w-1">
                    <a href="javascript:show_edit_sync_path_modal('{{ Id }}')" title="编辑" data-bs-toggle="tooltip">
                      <i class="ti ti-edit fs-2"></i>
                    </a>
                  </td>
                  <td class="w-1">
                    <a href="javascript:delete_sync_path('{{ Id }}')" title="删除" data-bs-toggle="tooltip">
                      <i class="ti ti-x fs-2"></i>
                    </a>
                  </td>
                </tr>
                {% endfor %}
              </tbody>
            </table>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
<div class="modal modal-blur fade" id="modal-directory" tabindex="-1" role="dialog" aria-hidden="true"
     data-bs-backdrop="static" data-bs-keyboard="false">
  <div class="modal-dialog modal-lg modal-dialog-centered" role="document">
    <div class="modal-content">
      <div class="modal-header">
        <h5 class="modal-title" id="directory_modal_title"></h5>
        <input type="hidden" id="sync_path_sid">
        <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
      </div>
      <div class="modal-body">
        <div class="row">
          <div class="col-lg-6">
            <div class="mb-3">
              <label class="form-label required">源目录 <span class="form-help"
                                                                title="源目录为需要同步的目录，源目录必须配置"
                                                                data-bs-toggle="tooltip">?</span></label>
              <input type="text" value="" id="sync_path_from" class="form-control filetree-folders-only"
                         placeholder="需要监控的目录" autocomplete="off">
            </div>
          </div>
          <div class="col-lg-6">
            <div class="mb-3">
              <label class="form-label">目的目录 <span class="form-help"
                                                                title="目的目录为识别和改名后存放的目录，目的目录未配置时将自动识别分类并转移到媒体库对应目录中"
                                                                data-bs-toggle="tooltip">?</span></label>
              <input type="text" value="" id="sync_path_to" class="form-control filetree-folders-only"
                         placeholder="留空使用媒体库目录"
                         autocomplete="off">
            </div>
          </div>
          <div class="col-lg-6">
            <div class="mb-3">
              <label class="form-label">未识别目录 <span class="form-help"
                                                                title="未识别目录为无法识别时转移的目录，未识别目录下产生的文件程序不会主动清理，建议不配置，未识别记录可在媒体整理->手动识别功能下处理"
                                                                data-bs-toggle="tooltip">?</span></label>
              <input type="text" value="" id="sync_path_unknown" class="form-control filetree-folders-only"
                         placeholder="留空不转移未识别文件" autocomplete="off">
            </div>
          </div>
          <div class="col-lg-6">
            <div class="mb-3">
              <label class="form-label required">同步方式 <span class="form-help"
                                                                title="目录同步使用的文件转移方式，根据不同的程序版本可选项会有所不同。硬链接模式要求源目录和目的目录或媒体库目录在一个磁盘分区或者存储空间/共享文件夹，Docker运行时需要直接映射源目录和目的目录或媒体库目录的上级目录，否则docker仍然会认为是跨盘；移动模式会直接移动原文件，会影响做种，请谨慎使用；Rclone针对网盘场景，需要自行映射rclone配置目录到容器中（/nt/.config/rclone）或在容器内使用rclone config完成rclone配置，网盘配置名称必须为：NASTOOL，可自行通过Docker添加环境变量传递参数优化传输，参考：https://rclone.org/docs/#environment-variables；Minio针对S3/云原生场景，需要自行在容器内使用mc alias set NASTOOL http://your_domain_name_or_ip:port ACCESS_KEY SECRET_KEY完成minio配置(alias的名称必须为NASTOOL)，并在minio控制台增加一个名为data的bucket(名称必须为data)"
                                                                data-bs-toggle="tooltip">?</span></label>
              <select id="sync_path_syncmod" class="form-select">
                {% for mode in RmtModeDict %}
                <option value="{{ mode.value }}">{{ mode.name }}</option>
                {% endfor %}
              </select>
            </div>
          </div>
        </div>
        <div class="row">
          <div class="col-lg-4 mb-1">
            <label class="form-check form-switch">
              <input class="form-check-input" type="checkbox" id="sync_path_compatibility" checked>
              <span class="form-check-label">兼容模式 <span class="form-help"
                                                                title="开启兼容模式后，目录同步可以实时监控挂载网盘、跨系统SMB共享等场景，但监控性能会降低，会增加对磁盘的访问量"
                                                                data-bs-toggle="tooltip">?</span></span>
            </label>
          </div>
          <div class="col-lg-4 mb-1">
            <label class="form-check form-switch">
              <input class="form-check-input" type="checkbox" id="sync_path_rename" checked>
              <span class="form-check-label">识别并重命名</span>
            </label>
          </div>
          <div class="col-lg-4 mb-1">
            <label class="form-check form-switch">
              <input class="form-check-input" type="checkbox" id="sync_path_enabled" checked>
              <span class="form-check-label">开启同步</span>
            </label>
          </div>
        </div>
      </div>
      <div class="modal-footer">
        <button type="button" class="btn btn-link me-auto" data-bs-dismiss="modal">取消</button>
        <a href="javascript:add_or_edit_sync_path()" id="directory_save_btn" class="btn btn-primary">确定</a>
      </div>
    </div>
  </div>
</div>
<script type="text/javascript">
  // 打开新增窗口
  function show_add_sync_path_modal() {
    $("#directory_modal_title").text('新增同步目录');
    $("#sync_path_sid").val('');
    $("#sync_path_from").val('');
    $("#sync_path_to").val('');
    $("#sync_path_unknown").val('');
    $("#sync_path_compatibility").prop("checked", false);
    $("#sync_path_rename").prop("checked", true);
    $("#sync_path_enabled").prop("checked", true);
    $("#modal-directory").modal('show');
  }

  // 打开编辑窗口
  function show_edit_sync_path_modal(sid) {
    axios_post_do("get_sync_path", {sid: sid}, function (ret) {
      if (ret.code === 0) {
        let sync_item = ret.result;
        $("#directory_modal_title").text('编辑同步目录');
        $("#sync_path_sid").val(sid);
        $("#sync_path_from").val(sync_item.from);
        $("#sync_path_to").val(sync_item.to);
        $("#sync_path_unknown").val(sync_item.unknown);
        $("#sync_path_syncmod").val(sync_item.syncmod);
        $("#sync_path_compatibility").prop("checked", sync_item.compatibility);
        $("#sync_path_rename").prop("checked", sync_item.rename);
        $("#sync_path_enabled").prop("checked", sync_item.enabled);
        $("#modal-directory").modal('show');
      }
    });
  }

  // 新增目录
  function add_or_edit_sync_path() {
    const params = input_select_GetVal("modal-directory", "sync_path_");
    if (!params.from) {
      $("#sync_path_from").addClass("is-invalid");
      return;
    } else {
      $("#sync_path_from").removeClass("is-invalid");
    }
    if (!params.rename) {
      if (!params.to) {
        $("#sync_path_to").addClass("is-invalid");
        return;
      } else {
        $("#sync_path_to").removeClass("is-invalid");
      }
    }
    $("#modal-directory").modal('hide');
    axios_post_do("add_or_edit_sync_path", params, function (ret) {
      if (ret.code === 0) {
        window_history_refresh();
      } else {
        show_fail_modal(ret.msg, function () {
          $("#modal-directory").modal('show');
        });
      }
    });
  }

  // 删除目录
  function delete_sync_path(sid) {
    axios_post_do("delete_sync_path", {sid: sid}, function (ret) {
      if (ret.code === 0) {
        window_history_refresh();
      }
    });
  }

  // 更新目录
  function check_sync_path(flag, sid, checked) {
    axios_post_do("check_sync_path", {flag: flag, sid: sid, checked: checked}, function (ret) {
      if (ret.code === 0) {
        window_history_refresh();
      }
    });
  }


</script>