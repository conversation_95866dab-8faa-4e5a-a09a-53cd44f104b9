<div class="container-xl">
  <!-- Page title -->
  <div class="page-header d-print-none">
    <div class="row align-items-center">
      <div class="col">
        <h2 class="page-title">
          用户管理
        </h2>
      </div>
      <div class="col-auto ms-auto d-print-none">
        <div class="btn-list">
          <a href="javascript:show_user_modal()" class="btn btn-primary d-none d-sm-inline-block">
            <i class="ti ti-plus fs-2"></i>
            新增用户
          </a>
          <a href="javascript:show_user_modal()" class="btn btn-primary d-sm-none btn-icon">
            <i class="ti ti-plus fs-2"></i>
          </a>
        </div>
      </div>
    </div>
  </div>
</div>
<!-- 业务页面代码 -->
{% if Users %}
<div class="page-body">
  <div class="container-xl">
    <div class="d-grid gap-3 grid-normal-card">
      {% for User in Users %}
        <div class="card card-link-pop rounded-3 overflow-hidden">
          <div class="card-body pt-4 text-center">
            <span class="avatar avatar-xl mb-3 rounded-circle">{{ User.name|first }}</span>
            <h3 class="m-0 mb-1"><a href="#">{{ User.name }}</a></h3>
            <div class="text-muted">{% if "系统设置" in User.pris %}<strong>管理员</strong>{% else %}普通用户{% endif %}</div>
            <div class="mt-3">
              {% for pri in User.pris %}
                <span class="badge mb-1">{{ pri }}</span>
              {% endfor %}
            </div>
          </div>
          <div class="d-flex">
            <a href="javascript: delete_user('{{ User.name }}')" class="card-btn text-danger d-sm-inline-flex">
              <i class="ti ti-trash fs-2"></i>
               删除
            </a>
          </div>
        </div>
      {% endfor %}
    </div>
  </div>
</div>
{% else %}
  <nodata-found title="没有用户" text='没有添加任何用户，请点击”新增用户“按钮。'></nodata-found>
{% endif %}
<div class="modal modal-blur fade" id="modal-user" tabindex="-1" role="dialog" aria-hidden="true"
     data-bs-backdrop="static" data-bs-keyboard="false">
  <div class="modal-dialog modal-lg modal-dialog-centered" role="document">
    <div class="modal-content">
      <div class="modal-header">
        <h5 class="modal-title">新增用户</h5>
        <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
      </div>
      <div class="modal-body">
        <div class="row">
          <div class="col-lg-6">
            <div class="mb-3">
              <label class="form-label required">用户名</label>
              <input type="text" value="" id="user_name" class="form-control">
            </div>
          </div>
          <div class="col-lg-6">
            <div class="mb-3">
              <label class="form-label required">密码</label>
              <input type="password" value="" id="user_password" class="form-control">
            </div>
          </div>
        </div>
        <div class="row">
          <div class="mb-3">
            <label class="form-label required">权限</label>
            <div class="form-selectgroup">
              {% for Menu in TopMenus %}
              <label class="form-selectgroup-item">
                <input type="checkbox" name="user_pris" value="{{ Menu }}" class="form-selectgroup-input">
                <span class="form-selectgroup-label">{{ Menu }}</span>
              </label>
              {% endfor %}
            </div>
          </div>
        </div>
      </div>
      <div class="modal-footer">
        <button type="button" class="btn btn-link me-auto" data-bs-dismiss="modal">取消</button>
        <a href="javascript:add_user()" id="add_user_btn" class="btn btn-primary">确定</a>
      </div>
    </div>
  </div>
</div>
<script type="text/javascript">
  // 打开新增窗口
  function show_user_modal() {
    $("#modal-user").modal('show');
  }

  // 新增用户
  function add_user() {
    const name = $("#user_name").val();
    const password = $("#user_password").val();
    let pris = select_GetSelectedVAL("user_pris");
    if (!name) {
      $("#user_name").addClass("is-invalid");
      return;
    } else {
      $("#user_name").removeClass("is-invalid");
    }
    if (!password) {
      $("#user_password").addClass("is-invalid");
      return;
    } else {
      $("#user_password").removeClass("is-invalid");
    }
    if (pris.length === 0) {
      return;
    }
    $("#modal-user").modal('hide');
    const params = {"oper": "add", "name": name, "password": password, "pris": pris};
    axios_post_do("user_manager", params, function (ret) {
      window_history_refresh();
    });
  }

  // 删除用户
  function delete_user(name) {
    const params = {"oper": "del", "name": name};
    axios_post_do("user_manager", params, function (ret) {
      window_history_refresh();
    });
  }
</script>