<div class="container-xl">
  <!-- Page title -->
  <div class="page-header d-print-none">
    <div class="row align-items-center">
      <div class="col">
        <h2 class="page-title" id="recommend_title"></h2>
        <div class="text-muted mt-1" id="recommend_subtitle"></div>
      </div>
    </div>
  </div>
</div>
<!-- 业务页面代码 -->
<div class="page-body">
  <div class="container-xl">
    <div class="d-grid gap-3 grid-media-card" id="recommend_content">
    </div>
  </div>
</div>
<script type="text/javascript">
  // 当前页码
  CurrentPage = {{ CurrentPage or 1 }};

  // 类型
  // MOV/TV/BANGUMI/SEARCH/DOWNLOADED/TRENDING/DISCOVER/DOUBANTAG
  Type = '{{ Type or "" }}';

  // 子类型
  SubType = '{{ SubType or "" }}';

  // 标题
  Title = '{{ Title or "" }}';
  $("#recommend_title").text(Title);

  // 子标题
  SubTitle = '{{ SubTitle or "" }}';
  if (SubTitle) {
    $("#recommend_subtitle").text(SubTitle).show();
  } else {
    $("#recommend_subtitle").hide();
  }

  // 星期
  Week = '{{ Week or "" }}';

  // TMDBID
  TmdbId = '{{ TmdbId or "" }}';

  // 演员ID
  PersonId = '{{ PersonId or "" }}';

  // 关键字
  Keyword = '{{ Keyword or "" }}';

  // 来源
  Source = '{{ Source or "" }}';

  // 过滤器
  Filter = '{{ Filter or "" }}';

  // 过滤参数
  Params = {{ Params|safe or "{}" }}

  // 插入N个占位卡片 代替loading动画
  var page_card_num = 20;
  function add_loading_card_placeholder() {
    for (let i = 0; i < page_card_num; i++) {
      let html = '<div id="loading_card_placeholder_' + i + '"><normal-card-placeholder></normal-card-placeholder></div>';
      $("#recommend_content").append(html);
    }
  }

  // 移除占位卡片
  function del_loading_card_placeholder() {
    $("div[id^='loading_card_placeholder_']").each(function(){
      $(this).remove();
    })
  }


  //状态标记
  var loading = false;
  function loading_data() {
    if (loading || CurrentPage > 10) {
      return;
    }
    loading = true;
    add_loading_card_placeholder();
    axios_post_do("get_recommend", {
      "type": Type,
      "subtype": SubType,
      "page": CurrentPage,
      "week": Week,
      "tmdbid": TmdbId,
      "personid": PersonId,
      "keyword": Keyword,
      "source": Source,
      "params": Params
    }, function (ret) {
      if (ret.Items.length > 0) {
        // 记录页码
        CurrentPage++;
        sessionStorage.CurrentPage = CurrentPage;
        loading = false;
        page_card_num = ret.Items.length;
      } else {
        if (CurrentPage === 1) {
          $("#recommend_content").removeClass("grid-media-card").html($("#no_recommend_html").text());
        }
      }
      del_loading_card_placeholder();
      // 插入HTML
      for (let i = 0; i < ret.Items.length; i++) {
        let item = ret.Items[i];
        let html = `<normal-card
                      card-tmdbId="${item.id}"
                      card-mediatype="${item.type}"
                      card-showSub="1"
                      card-image="${item.image}"
                      card-weekday="${item.weekday}"
                      card-fav="${item.fav}"
                      card-vote="${item.vote}"
                      card-year="${item.year}"
                      card-title="${item.title}"
                      card-overview="${item.overview}"
                      card-restype="${item.media_type}"
                      card-date="${item.date}"
                      card-site="${item.site}"
                    ></normal-card>`;
        $("#recommend_content").append(html);
      }
      window_history();
    });
  }


  // 如果没有滚动条则继续加载
  function loading_data_max() {
    // 加入延迟是因为需要确保组件已经完成渲染才能正确计算可滚动高度
    setTimeout(() => {
      if (!hasScrollbar()) {
        loading_data();
      }
    }, 200);
  }

  // 后退时sessionStorage.CurrentPage 未被清除, 则更新当前页码
  if (sessionStorage.CurrentPage) {
    CurrentPage = sessionStorage.CurrentPage;
    loading_data_max();
  } else {
    // 否则初始化数据
    loading_data();
  }

  // 带过滤参数刷新页面
  function filter_refresh(filterkey, value) {
    Params[filterkey] = value;
    navmenu(`recommend?type=${Type}&subtype=${SubType}&title=${Title}&subtitle=${SubTitle}&filter=${Filter}&params=${JSON.stringify(Params)}`)
  }


  // 滚动加载事件
  $(document).ready(function ($) {
    $(window).scroll(function(){
      if(getScrollRate() < 0.01){
        loading_data();
      }
    });
  });

</script>
<script type="text/html" id="no_recommend_html">
<empty-content title="没有数据" text=""></empty-content>
</script>